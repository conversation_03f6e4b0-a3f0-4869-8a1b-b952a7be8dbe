# ========================================
# CONFIGURAÇÕES DE AMBIENTE - SISTEMA COMPLETO
# ========================================
#
# Este arquivo contém todas as configurações não-sensíveis do sistema.
# Copie este arquivo para .env e configure os valores apropriados.
#
# IMPORTANTE:
# - Credenciais sensíveis devem ser configuradas via Firebase Functions config
# - Este arquivo deve estar no .gitignore
# - Nunca commitar valores reais de produção
#

# ========================================
# CONFIGURAÇÕES DE AMBIENTE GERAL
# ========================================

# Ambiente de execução
NODE_ENV=development

# ========================================
# CONFIGURAÇÕES DE CHAT API
# ========================================

# URL da API de chat
CHAT_API_URL=https://shotxv2dev.qi.plus

# ========================================
# CONFIGURAÇÕES DE REDIS
# ========================================

# Host do Redis
REDIS_HOST=localhost

# Porta do Redis
REDIS_PORT=6379

# Senha do Redis (use Firebase config para produção)
REDIS_PASSWORD=your_redis_password

# Database do Redis
REDIS_DATABASE=0

# Timeout de conexão Redis (millisegundos)
REDIS_TIMEOUT=10000

# ========================================
# CONFIGURAÇÕES DE DOMÍNIOS DE EMAIL
# ========================================

# Domínio principal da aplicação
EMAIL_APP_DOMAIN=qiplus.cloud

# Domínio para envio de emails (produção)
MAILING_DOMAIN=mail.qiplus.com.br

# Domínio para envio de emails (desenvolvimento)
DEV_MAILING_DOMAIN=devmail.qiplus.com.br

# ========================================
# CONFIGURAÇÕES SMTP
# ========================================

# Servidor SMTP
SMTP_HOST=smtp.zoho.com
SMTP_PORT=465
SMTP_ENCRYPTION=ssl

# Usuário SMTP (não-sensível, pode ser email público)
SMTP_ADMIN_USER=<EMAIL>

# Nome do remetente
SMTP_ADMIN_NAME=Admin || QIPlus

# Serviço SMTP (opcional, para provedores conhecidos como Gmail, Yahoo)
SMTP_SERVICE=

# ========================================
# CONFIGURAÇÕES DE PERFORMANCE DE EMAIL
# ========================================

# Tamanho do lote para processamento de emails
EMAIL_BATCH_SIZE=50

# Timeout para envio de emails (segundos)
EMAIL_TIMEOUT_SECONDS=30

# ========================================
# CONFIGURAÇÕES DE RETRY DE EMAIL
# ========================================

# Número máximo de tentativas de retry
EMAIL_RETRY_ATTEMPTS=3

# Delay inicial para retry (millisegundos)
EMAIL_RETRY_DELAY_MS=1000

# Multiplicador para backoff exponencial
EMAIL_RETRY_BACKOFF_MULTIPLIER=2

# Delay máximo para retry (millisegundos)
EMAIL_MAX_RETRY_DELAY_MS=30000

# ========================================
# CONFIGURAÇÕES DE FILAS REDIS
# ========================================

# Habilitar/desabilitar uso do Redis para filas de email
EMAIL_USE_REDIS=true

# Chave para fila de emails agendados
EMAIL_REDIS_QUEUE_KEY=email:scheduled_emails

# Chave para dead letter queue de emails
EMAIL_REDIS_DEAD_LETTER_KEY=email:dead_letter_queue

# Chave para emails em processamento
EMAIL_REDIS_PROCESSING_KEY=email:processing

# ========================================
# CONFIGURAÇÕES DE MONITORAMENTO
# ========================================

# Habilitar métricas de performance
EMAIL_METRICS_ENABLED=true

# Nível de logging (debug, info, warn, error)
EMAIL_LOGGING_LEVEL=info

# ========================================
# CONFIGURAÇÕES ESPECÍFICAS DO MAILGUN
# ========================================

# Limite de dias para agendamento no Mailgun
MAILGUN_SCHEDULE_LIMIT_DAYS=3

# ========================================
# CONFIGURAÇÕES DE DESENVOLVIMENTO
# ========================================

# Habilitar modo dry run por padrão
EMAIL_DRY_RUN=false

# ========================================
# CREDENCIAIS SENSÍVEIS (VIA FIREBASE CONFIG)
# ========================================
#
# As seguintes credenciais devem ser configuradas via Firebase Functions config:
#
# firebase functions:config:set \
#   mailgun.api_key="SUA_CHAVE_MAILGUN" \
#   mailgun.public_key="SUA_CHAVE_PUBLICA_MAILGUN" \
#   mailgun.password="SUA_SENHA_MAILGUN" \
#   resend.apikey="SUA_CHAVE_RESEND" \
#   smtp.admin_pass="SUA_SENHA_SMTP" \
#   redis.password="SUA_SENHA_REDIS" \
#   pagarme.api_key_live="SUA_CHAVE_PAGARME_LIVE" \
#   pagarme.api_key_test="SUA_CHAVE_PAGARME_TEST" \
#   pagarme.encrypt_key_live="SUA_CHAVE_ENCRYPT_LIVE" \
#   pagarme.encrypt_key_test="SUA_CHAVE_ENCRYPT_TEST" \
#   chat.api_key="SUA_CHAVE_CHAT" \
#   bitly.token="SEU_TOKEN_BITLY" \
#   notazz.api_key_prod="SUA_CHAVE_NOTAZZ_PROD" \
#   notazz.api_key_dev="SUA_CHAVE_NOTAZZ_DEV" \
#   google.client_id="SEU_CLIENT_ID_GOOGLE"
#
# ========================================
# VERIFICAÇÃO DE CONFIGURAÇÃO
# ========================================
#
# Para verificar se todas as configurações estão corretas, execute:
#
# const { validateEmailConfig } = require('./config/emailConfig');
# const { performSecurityAudit } = require('./config/securityConfig');
#
# validateEmailConfig();
# performSecurityAudit();
#
