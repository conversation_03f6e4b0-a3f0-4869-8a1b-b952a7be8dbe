const { momentNow } = require("../helpers");
const { FirestoreRef, CONSTANTS, COLLECTIONS, moment } = require("../init");
const { MOMENT_ISO } = CONSTANTS;
const axios = require("axios");
const { getLeadById, getSegmentationsByIds } = require("../post");
const { getIgIdByInstance } = require("../utils/instacesUtils");
const { getLeadsIdsFromSegmentation } = require("../leads");
const { shotxOrganizeMessages } = require("../shotxOrganize");
const { shotxSendMessages } = require("../shotxSendMessages");
const shotxCron = async () => {
  let now = momentNow().add(5, "minutes").format(MOMENT_ISO);
  console.log("SHOTXCRON > START");

  const fetchSchedule = async () => {
    let scheduled = [];

    await FirestoreRef.collection(COLLECTIONS.SHOTX_CRON_COLLECTION_NAME)
      .where("status", "==", "published")
      .where("scheduled_date", "<=", now)
      .get()
      .then((snapshot) => {
        return snapshot.docs.map((doc) => {
          scheduled.push(doc.data());
          doc.ref.update({
            modified_date: momentNow().format(MOMENT_ISO),
            status: "sent",
          });
          return scheduled;
        });
      });
    console.log("SHOTXCRON > FETCHED", scheduled.length);
    return scheduled;
  };

  return fetchSchedule().then(async (schedules) => {
    if (schedules && schedules.length > 0) {
      let preparedSchedules = [];
      try {
        for (const appointment of schedules) {
          console.log("SHOTXCRON > PREPARE > APP", appointment);
          const prepared = await prepareLeadsOnSchedules(appointment);
          FirestoreRef.collection(COLLECTIONS.SHOTX_CRON_COLLECTION_NAME)
            .doc(appointment.ID)
            .update({ segmentationsIds: prepared.segmentationsIds });
          console.log("SHOTXCRON > PREPARE > PREPARED SCHEDULE", prepared);
          preparedSchedules = [
            ...preparedSchedules,
            ...prepared.preparedSchedules,
          ];
        }
      } catch (error) {
        console.log("SHOTXCRON > PREPARE > ERROR", error);
      }

      shotxOrganizeMessages(preparedSchedules);
    }
    return null;
  });
};

const prepareLeadsOnSchedules = async (schedule) => {
  const leads = schedule.contacts || [];
  console.log("SHOTXCRON > PREPARE > LEADS", leads);

  if ((!leads || leads.length === 0) && !schedule.segmentations) {
    console.log("SHOTXCRON > PREPARE > NO LEADS", schedule);
    return null;
  }

  const preparedSchedules = [];
  const contacts = [];
  const segmentationsIds = [];
  if (schedule.hasOwnProperty("segmentations")) {
    const segmentations = await getSegmentationsByIds(schedule.segmentations);

    for (const segmentation of Object.values(segmentations)) {
      let contactIds;

      if (segmentation.config.dinamic) {
        console.log("SHOTXCRON > PREPARE > SEGMENTATION DINAMIC", segmentation);
        contactIds = await getLeadsIdsFromSegmentation(segmentation);
        segmentationsIds.push(...contactIds.data.contactIds);
        leads.push(...contactIds.data.contactIds);
      } else {
        console.log("SHOTXCRON > PREPARE > SEGMENTATION STATIC", segmentation);
        contactIds = segmentation.leads;
        console.log(
          "SHOTXCRON > PREPARE > SEGMENTATION STATIC",
          segmentation.leads
        );
        leads.push(...contactIds);
        segmentationsIds.push(...contactIds);
      }
      console.log(
        "SHOTXCRON > PREPARE > LEADS IDS FROM SEGMENTATION",
        contactIds
      );
    }
  }

  // console.log("SHOTXCRON > PREPARE > segmentatios", segmentatios);

  for (const leadId of leads) {
    const lead = (await getLeadById(leadId)) || {};
    if (lead) {
      let contactRemoteId;
      let contactName;

      switch (schedule.instance.platform) {
        case "Whatsapp":
          contactName = lead.displayName;
          contactRemoteId = lead.mobileCC + lead.mobile;
          contacts.push({
            id: leadId,
            contactName,
            contactRemoteId,
          });
          break;
        case "Instagram":
          contactName = lead.displayName;
          contactRemoteId = getIgIdByInstance(lead, schedule.instance);
          contacts.push({
            id: leadId,
            contactName,
            contactRemoteId,
          });
          break;
        default:
          break;
      }
    }
  }
  const schedulePrepared = {
    ...schedule,
    contacts,
  };

  preparedSchedules.push(schedulePrepared);
  return {
    preparedSchedules,
    segmentationsIds,
  };
};

module.exports = {
  shotxCron,
};
