/**
 * GERENCIADOR DE PROVEDORES DE EMAIL
 * 
 * Este arquivo implementa o gerenciamento unificado dos provedores de email
 * (Mailgun, Resend, SMTP), fornecendo interface consistente para envio.
 * 
 * FUNÇÃO NO SISTEMA:
 * - Fornecer interface unificada para todos os provedores
 * - Implementar lógica específica de cada provedor
 * - Gerenciar configurações e credenciais de forma segura
 * - Padronizar respostas de todos os provedores
 */

const { MAILGUN_CONFIG, RESEND_CONFIG, SMTP_CONFIG } = require("../config/emailConfig");

/**
 * CLASSE BASE PARA PROVEDORES DE EMAIL
 * Define interface comum para todos os provedores
 */
class EmailProvider {
  constructor(name, config) {
    this.name = name;
    this.config = config;
  }

  async sendEmail(emailData) {
    throw new Error(`sendEmail deve ser implementado pela classe ${this.name}`);
  }

  async validateConfig() {
    throw new Error(`validateConfig deve ser implementado pela classe ${this.name}`);
  }
}

/**
 * PROVEDOR MAILGUN
 * Implementa envio via Mailgun API
 */
class MailgunProvider extends EmailProvider {
  constructor() {
    super('mailgun', MAILGUN_CONFIG);
    this.mailgun = null;
  }

  async initializeClient() {
    if (!this.mailgun) {
      const mailgunjs = require("mailgun-js");
      this.mailgun = mailgunjs({
        apiKey: this.config.apiKey,
        domain: this.config.defaultDomain,
        host: 'api.mailgun.net', // ou 'api.eu.mailgun.net' para EU
      });
    }
    return this.mailgun;
  }

  async sendEmail(emailData) {
    try {
      const mg = await this.initializeClient();

      // Preparar dados para Mailgun
      const mailgunData = {
        from: emailData.from,
        to: emailData.to,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text,
      };

      // Campos opcionais
      if (emailData.cc) mailgunData.cc = emailData.cc;
      if (emailData.bcc) mailgunData.bcc = emailData.bcc;
      if (emailData.replyTo) mailgunData['h:Reply-To'] = emailData.replyTo;

      // Tags para tracking
      if (emailData.tags && emailData.tags.length > 0) {
        mailgunData['o:tag'] = emailData.tags;
      }

      // Tracking
      if (emailData.trackOpens) mailgunData['o:tracking-opens'] = 'yes';
      if (emailData.trackClicks) mailgunData['o:tracking-clicks'] = 'yes';

      // Headers customizados
      if (emailData.headers) {
        Object.keys(emailData.headers).forEach(key => {
          mailgunData[`h:${key}`] = emailData.headers[key];
        });
      }

      console.log('MAILGUN > Enviando email:', {
        to: emailData.to,
        subject: emailData.subject,
        from: emailData.from,
      });

      const result = await mg.messages().send(mailgunData);

      console.log('MAILGUN > Email enviado com sucesso:', result.id);

      return {
        success: true,
        messageId: result.id,
        provider: 'mailgun',
        response: result,
      };

    } catch (error) {
      console.error('MAILGUN > Erro ao enviar email:', error.message);
      throw new Error(`Mailgun: ${error.message}`);
    }
  }

  async validateConfig() {
    const errors = [];
    
    if (!this.config.apiKey) {
      errors.push('Mailgun API Key não configurada');
    }
    
    if (!this.config.defaultDomain) {
      errors.push('Mailgun Domain não configurado');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

/**
 * PROVEDOR RESEND
 * Implementa envio via Resend API
 */
class ResendProvider extends EmailProvider {
  constructor() {
    super('resend', RESEND_CONFIG);
    this.resend = null;
  }

  async initializeClient() {
    if (!this.resend) {
      const { Resend } = require("resend");
      this.resend = new Resend(this.config.apiKey);
    }
    return this.resend;
  }

  async sendEmail(emailData) {
    try {
      const resend = await this.initializeClient();

      // Preparar dados para Resend
      const resendData = {
        from: emailData.from,
        to: [emailData.to],
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text,
      };

      // Campos opcionais
      if (emailData.cc) resendData.cc = Array.isArray(emailData.cc) ? emailData.cc : [emailData.cc];
      if (emailData.bcc) resendData.bcc = Array.isArray(emailData.bcc) ? emailData.bcc : [emailData.bcc];
      if (emailData.replyTo) resendData.reply_to = [emailData.replyTo];

      // Tags para tracking
      if (emailData.tags && emailData.tags.length > 0) {
        resendData.tags = emailData.tags.map(tag => ({ name: tag, value: 'true' }));
      }

      // Headers customizados
      if (emailData.headers) {
        resendData.headers = emailData.headers;
      }

      console.log('RESEND > Enviando email:', {
        to: emailData.to,
        subject: emailData.subject,
        from: emailData.from,
      });

      const result = await resend.emails.send(resendData);

      console.log('RESEND > Email enviado com sucesso:', result.data?.id);

      return {
        success: true,
        messageId: result.data?.id,
        provider: 'resend',
        response: result,
      };

    } catch (error) {
      console.error('RESEND > Erro ao enviar email:', error.message);
      throw new Error(`Resend: ${error.message}`);
    }
  }

  async validateConfig() {
    const errors = [];
    
    if (!this.config.apiKey) {
      errors.push('Resend API Key não configurada');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

/**
 * PROVEDOR SMTP
 * Implementa envio via SMTP genérico
 */
class SMTPProvider extends EmailProvider {
  constructor() {
    super('smtp', SMTP_CONFIG);
    this.transporter = null;
  }

  async initializeClient() {
    if (!this.transporter) {
      const nodemailer = require("nodemailer");
      
      const transportConfig = {
        host: this.config.host,
        port: this.config.port,
        secure: this.config.encryption === 'ssl', // true para 465, false para outros
        auth: {
          user: this.config.adminUser,
          pass: this.config.adminPass,
        },
      };

      // Configuração específica para serviços conhecidos
      if (this.config.service) {
        transportConfig.service = this.config.service;
      }

      this.transporter = nodemailer.createTransporter(transportConfig);
    }
    return this.transporter;
  }

  async sendEmail(emailData) {
    try {
      const transporter = await this.initializeClient();

      // Preparar dados para SMTP
      const smtpData = {
        from: emailData.from,
        to: emailData.to,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text,
      };

      // Campos opcionais
      if (emailData.cc) smtpData.cc = emailData.cc;
      if (emailData.bcc) smtpData.bcc = emailData.bcc;
      if (emailData.replyTo) smtpData.replyTo = emailData.replyTo;

      // Headers customizados
      if (emailData.headers) {
        smtpData.headers = emailData.headers;
      }

      console.log('SMTP > Enviando email:', {
        to: emailData.to,
        subject: emailData.subject,
        from: emailData.from,
      });

      const result = await transporter.sendMail(smtpData);

      console.log('SMTP > Email enviado com sucesso:', result.messageId);

      return {
        success: true,
        messageId: result.messageId,
        provider: 'smtp',
        response: result,
      };

    } catch (error) {
      console.error('SMTP > Erro ao enviar email:', error.message);
      throw new Error(`SMTP: ${error.message}`);
    }
  }

  async validateConfig() {
    const errors = [];
    
    if (!this.config.host) {
      errors.push('SMTP Host não configurado');
    }
    
    if (!this.config.adminUser) {
      errors.push('SMTP User não configurado');
    }
    
    if (!this.config.adminPass) {
      errors.push('SMTP Password não configurada');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

/**
 * INSTÂNCIAS DOS PROVEDORES
 */
const providers = {
  mailgun: new MailgunProvider(),
  resend: new ResendProvider(),
  smtp: new SMTPProvider(),
};

/**
 * OBTER GERENCIADOR DE PROVEDOR
 * Retorna instância do provedor solicitado
 * 
 * @param {string} providerName - Nome do provedor
 * @returns {EmailProvider|null} Instância do provedor
 */
const getProviderManager = (providerName) => {
  const provider = providers[providerName.toLowerCase()];
  if (!provider) {
    console.error(`PROVIDERMANAGER > Provedor não encontrado: ${providerName}`);
    return null;
  }
  return provider;
};

/**
 * VALIDAR TODOS OS PROVEDORES
 * Verifica configuração de todos os provedores
 * 
 * @returns {Promise<Object>} Resultado da validação
 */
const validateAllProviders = async () => {
  const results = {};
  
  for (const [name, provider] of Object.entries(providers)) {
    try {
      results[name] = await provider.validateConfig();
    } catch (error) {
      results[name] = {
        valid: false,
        errors: [error.message],
      };
    }
  }
  
  return results;
};

/**
 * TESTAR CONECTIVIDADE DE TODOS OS PROVEDORES
 * Testa se todos os provedores estão funcionando
 * 
 * @returns {Promise<Object>} Resultado dos testes
 */
const testAllProviders = async () => {
  const results = {};
  
  for (const [name, provider] of Object.entries(providers)) {
    try {
      // Tentar inicializar cliente
      await provider.initializeClient();
      results[name] = { connected: true };
    } catch (error) {
      results[name] = { 
        connected: false, 
        error: error.message 
      };
    }
  }
  
  return results;
};

/**
 * EXPORTS
 */
module.exports = {
  // Classes
  EmailProvider,
  MailgunProvider,
  ResendProvider,
  SMTPProvider,
  
  // Funções principais
  getProviderManager,
  validateAllProviders,
  testAllProviders,
  
  // Instâncias
  providers,
};
