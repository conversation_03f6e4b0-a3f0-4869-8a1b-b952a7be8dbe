/**
 * PROCESSADOR DE EMAILS INDIVIDUAL
 * 
 * Este arquivo implementa o processamento individual de emails, incluindo
 * seleção de provedor, formatação de dados e envio efetivo.
 * 
 * FUNÇÃO NO SISTEMA:
 * - Processar um email individual através do provedor apropriado
 * - Implementar fallback entre provedores (Mailgun -> Resend -> SMTP)
 * - Formatar dados do email para cada provedor
 * - Retornar resultado padronizado do processamento
 */

const { MAILGUN_CONFIG, RESEND_CONFIG, SMTP_CONFIG } = require("../config/emailConfig");
const { getProviderManager } = require("./providerManager");

/**
 * PROCESSAR EMAIL INDIVIDUAL
 * Função principal para processar um email através do provedor apropriado
 * 
 * @param {Object} emailData - Dados do email a ser processado
 * @param {Object} options - Opções de processamento
 * @returns {Promise<Object>} Resultado do processamento
 */
const processEmail = async (emailData, options = {}) => {
  try {
    const {
      preferredProvider = 'mailgun',
      enableFallback = true,
      timeout = 30000,
    } = options;

    console.log(`EMAILPROCESSOR > Processando email: ${emailData.id || 'sem-id'}`);
    console.log(`EMAILPROCESSOR > Provedor preferido: ${preferredProvider}`);

    // Validar dados do email
    const validationResult = validateEmailData(emailData);
    if (!validationResult.valid) {
      return {
        success: false,
        error: `Dados do email inválidos: ${validationResult.errors.join(', ')}`,
        provider: null,
        emailId: emailData.id,
      };
    }

    // Normalizar dados do email
    const normalizedEmail = normalizeEmailData(emailData);

    // Tentar envio com provedor preferido
    let result = await attemptEmailSend(normalizedEmail, preferredProvider, timeout);

    // Se falhou e fallback está habilitado, tentar outros provedores
    if (!result.success && enableFallback) {
      const fallbackProviders = getFallbackProviders(preferredProvider);
      
      for (const provider of fallbackProviders) {
        console.log(`EMAILPROCESSOR > Tentando fallback com provedor: ${provider}`);
        result = await attemptEmailSend(normalizedEmail, provider, timeout);
        
        if (result.success) {
          console.log(`EMAILPROCESSOR > Sucesso com provedor fallback: ${provider}`);
          break;
        }
      }
    }

    // Log do resultado final
    if (result.success) {
      console.log(`EMAILPROCESSOR > Email enviado com sucesso via ${result.provider}`);
    } else {
      console.error(`EMAILPROCESSOR > Falha ao enviar email: ${result.error}`);
    }

    return {
      ...result,
      emailId: emailData.id,
      originalData: emailData,
      processedAt: new Date().toISOString(),
    };

  } catch (error) {
    console.error('EMAILPROCESSOR > Erro no processamento:', error.message);
    return {
      success: false,
      error: error.message,
      provider: null,
      emailId: emailData.id,
      processedAt: new Date().toISOString(),
    };
  }
};

/**
 * TENTAR ENVIO DE EMAIL COM PROVEDOR ESPECÍFICO
 * 
 * @param {Object} emailData - Dados normalizados do email
 * @param {string} provider - Nome do provedor (mailgun, resend, smtp)
 * @param {number} timeout - Timeout em millisegundos
 * @returns {Promise<Object>} Resultado da tentativa
 */
const attemptEmailSend = async (emailData, provider, timeout) => {
  try {
    console.log(`EMAILPROCESSOR > Tentando envio via ${provider}`);

    // Obter gerenciador do provedor
    const providerManager = getProviderManager(provider);
    if (!providerManager) {
      return {
        success: false,
        error: `Provedor não suportado: ${provider}`,
        provider,
      };
    }

    // Configurar timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`Timeout de ${timeout}ms excedido`)), timeout);
    });

    // Tentar envio com timeout
    const sendPromise = providerManager.sendEmail(emailData);
    const result = await Promise.race([sendPromise, timeoutPromise]);

    return {
      success: true,
      provider,
      result,
      sentAt: new Date().toISOString(),
    };

  } catch (error) {
    console.error(`EMAILPROCESSOR > Erro ao enviar via ${provider}:`, error.message);
    return {
      success: false,
      error: error.message,
      provider,
      attemptedAt: new Date().toISOString(),
    };
  }
};

/**
 * VALIDAR DADOS DO EMAIL
 * Verifica se os dados obrigatórios estão presentes
 * 
 * @param {Object} emailData - Dados do email
 * @returns {Object} Resultado da validação
 */
const validateEmailData = (emailData) => {
  const errors = [];

  // Campos obrigatórios
  if (!emailData.to) {
    errors.push('Campo "to" é obrigatório');
  }

  if (!emailData.subject) {
    errors.push('Campo "subject" é obrigatório');
  }

  if (!emailData.html && !emailData.text) {
    errors.push('Campo "html" ou "text" é obrigatório');
  }

  // Validar formato de email
  if (emailData.to && !isValidEmail(emailData.to)) {
    errors.push('Formato de email inválido no campo "to"');
  }

  if (emailData.from && !isValidEmail(emailData.from)) {
    errors.push('Formato de email inválido no campo "from"');
  }

  return {
    valid: errors.length === 0,
    errors,
  };
};

/**
 * NORMALIZAR DADOS DO EMAIL
 * Padroniza os dados do email para todos os provedores
 * 
 * @param {Object} emailData - Dados originais do email
 * @returns {Object} Dados normalizados
 */
const normalizeEmailData = (emailData) => {
  // Determinar domínio padrão baseado no ambiente
  const defaultDomain = MAILGUN_CONFIG.defaultDomain;
  const defaultFromEmail = MAILGUN_CONFIG.defaultFromEmail;

  return {
    // Campos obrigatórios
    to: emailData.to,
    subject: emailData.subject,
    html: emailData.html || emailData.content,
    text: emailData.text || stripHtml(emailData.html || emailData.content || ''),

    // Campos opcionais com fallbacks
    from: emailData.from || defaultFromEmail,
    replyTo: emailData.replyTo || emailData.reply_to,
    cc: emailData.cc,
    bcc: emailData.bcc,

    // Metadados
    id: emailData.id,
    tags: emailData.tags || [],
    headers: emailData.headers || {},
    attachments: emailData.attachments || [],

    // Configurações específicas
    trackOpens: emailData.trackOpens !== false, // Padrão: true
    trackClicks: emailData.trackClicks !== false, // Padrão: true
    
    // Dados originais para referência
    _originalData: emailData,
  };
};

/**
 * OBTER PROVEDORES DE FALLBACK
 * Define a ordem de fallback baseada no provedor preferido
 * 
 * @param {string} preferredProvider - Provedor preferido
 * @returns {Array} Lista de provedores para fallback
 */
const getFallbackProviders = (preferredProvider) => {
  const allProviders = ['mailgun', 'resend', 'smtp'];
  return allProviders.filter(provider => provider !== preferredProvider);
};

/**
 * VALIDAR FORMATO DE EMAIL
 * Verifica se o email tem formato válido
 * 
 * @param {string} email - Email a ser validado
 * @returns {boolean} True se válido
 */
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * REMOVER HTML DE TEXTO
 * Converte HTML em texto simples
 * 
 * @param {string} html - HTML a ser convertido
 * @returns {string} Texto simples
 */
const stripHtml = (html) => {
  return html
    .replace(/<[^>]*>/g, '') // Remove tags HTML
    .replace(/&nbsp;/g, ' ') // Substitui &nbsp; por espaço
    .replace(/&amp;/g, '&') // Substitui &amp; por &
    .replace(/&lt;/g, '<') // Substitui &lt; por <
    .replace(/&gt;/g, '>') // Substitui &gt; por >
    .replace(/&quot;/g, '"') // Substitui &quot; por "
    .replace(/&#39;/g, "'") // Substitui &#39; por '
    .trim();
};

/**
 * EXPORTS
 */
module.exports = {
  processEmail,
  attemptEmailSend,
  validateEmailData,
  normalizeEmailData,
  getFallbackProviders,
  isValidEmail,
  stripHtml,
};
