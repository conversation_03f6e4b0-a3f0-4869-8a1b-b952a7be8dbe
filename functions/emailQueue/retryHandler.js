/**
 * GERENCIADOR DE RETRY E DEAD LETTER QUEUE
 * 
 * Este arquivo implementa a lógica de retry com backoff exponencial e
 * gerenciamento de dead letter queue para emails que falharam definitivamente.
 * 
 * FUNÇÃO NO SISTEMA:
 * - Implementar retry logic com backoff exponencial
 * - Gerenciar dead letter queue para emails falhados
 * - Determinar quando um email deve ser descartado
 * - Fornecer métricas de falhas e recuperação
 */

const { PERFORMANCE_CONFIG } = require("../config/emailConfig");
const { saveScheduledMessage } = require("../utils/redisClient");
const { FirestoreRef, CONSTANTS } = require("../init");
const { momentNow } = require("../helpers");

/**
 * GERENCIAR RETRY DE EMAIL FALHADO
 * Determina se um email deve ser tentado novamente ou movido para dead letter queue
 * 
 * @param {Object} emailData - Dados do email que falhou
 * @param {string} error - Erro que causou a falha
 * @returns {Promise<Object>} Resultado da decisão de retry
 */
const handleRetry = async (emailData, error) => {
  try {
    const currentAttempt = (emailData._retry_attempt || 0) + 1;
    const maxAttempts = PERFORMANCE_CONFIG.retryAttempts;

    console.log(`RETRYHANDLER > Analisando retry para email ${emailData.id}`);
    console.log(`RETRYHANDLER > Tentativa ${currentAttempt}/${maxAttempts}`);
    console.log(`RETRYHANDLER > Erro: ${error}`);

    // Verificar se ainda há tentativas disponíveis
    if (currentAttempt >= maxAttempts) {
      console.log(`RETRYHANDLER > Máximo de tentativas atingido para email ${emailData.id}`);
      return {
        shouldRetry: false,
        reason: 'max_attempts_reached',
        attempt: currentAttempt,
      };
    }

    // Verificar se o erro é recuperável
    const errorAnalysis = analyzeError(error);
    if (!errorAnalysis.recoverable) {
      console.log(`RETRYHANDLER > Erro não recuperável para email ${emailData.id}: ${errorAnalysis.reason}`);
      return {
        shouldRetry: false,
        reason: errorAnalysis.reason,
        attempt: currentAttempt,
      };
    }

    // Calcular delay do retry com backoff exponencial
    const retryDelay = calculateRetryDelay(currentAttempt);

    console.log(`RETRYHANDLER > Email ${emailData.id} será tentado novamente em ${retryDelay}ms`);

    return {
      shouldRetry: true,
      attempt: currentAttempt,
      retryDelay,
      reason: 'recoverable_error',
    };

  } catch (error) {
    console.error('RETRYHANDLER > Erro ao processar retry:', error.message);
    return {
      shouldRetry: false,
      reason: 'retry_processing_error',
      error: error.message,
    };
  }
};

/**
 * ANALISAR ERRO PARA DETERMINAR SE É RECUPERÁVEL
 * Classifica erros em recuperáveis e não-recuperáveis
 * 
 * @param {string} error - Mensagem de erro
 * @returns {Object} Análise do erro
 */
const analyzeError = (error) => {
  const errorMessage = error.toLowerCase();

  // Erros não-recuperáveis (não vale a pena tentar novamente)
  const nonRecoverablePatterns = [
    'invalid email',
    'email address is invalid',
    'recipient address rejected',
    'domain not found',
    'user unknown',
    'mailbox unavailable',
    'authentication failed',
    'invalid api key',
    'unauthorized',
    'forbidden',
    'bad request',
    'malformed',
    'syntax error',
  ];

  for (const pattern of nonRecoverablePatterns) {
    if (errorMessage.includes(pattern)) {
      return {
        recoverable: false,
        reason: 'non_recoverable_error',
        pattern,
      };
    }
  }

  // Erros temporários/recuperáveis
  const recoverablePatterns = [
    'timeout',
    'connection',
    'network',
    'temporary',
    'rate limit',
    'quota',
    'service unavailable',
    'internal server error',
    'bad gateway',
    'gateway timeout',
    'too many requests',
  ];

  for (const pattern of recoverablePatterns) {
    if (errorMessage.includes(pattern)) {
      return {
        recoverable: true,
        reason: 'temporary_error',
        pattern,
      };
    }
  }

  // Por padrão, considerar recuperável se não identificado
  return {
    recoverable: true,
    reason: 'unknown_error_assumed_recoverable',
  };
};

/**
 * CALCULAR DELAY DE RETRY COM BACKOFF EXPONENCIAL
 * Implementa backoff exponencial com jitter para evitar thundering herd
 * 
 * @param {number} attempt - Número da tentativa (1, 2, 3, ...)
 * @returns {number} Delay em millisegundos
 */
const calculateRetryDelay = (attempt) => {
  const baseDelay = PERFORMANCE_CONFIG.retryDelayMs;
  const backoffMultiplier = PERFORMANCE_CONFIG.retryBackoffMultiplier;
  const maxDelay = PERFORMANCE_CONFIG.maxRetryDelayMs;

  // Calcular delay exponencial: baseDelay * (backoffMultiplier ^ (attempt - 1))
  let delay = baseDelay * Math.pow(backoffMultiplier, attempt - 1);

  // Aplicar limite máximo
  delay = Math.min(delay, maxDelay);

  // Adicionar jitter (variação aleatória de ±25%) para evitar thundering herd
  const jitter = delay * 0.25 * (Math.random() - 0.5);
  delay = Math.round(delay + jitter);

  // Garantir que o delay seja pelo menos o mínimo
  delay = Math.max(delay, baseDelay);

  return delay;
};

/**
 * MOVER EMAIL PARA DEAD LETTER QUEUE
 * Move emails que falharam definitivamente para a dead letter queue
 * 
 * @param {Object} emailData - Dados do email
 * @param {string} finalError - Erro final que causou o descarte
 * @returns {Promise<boolean>} Sucesso da operação
 */
const moveToDeadLetter = async (emailData, finalError) => {
  try {
    console.log(`RETRYHANDLER > Movendo email ${emailData.id} para dead letter queue`);

    const deadLetterKey = PERFORMANCE_CONFIG.redisDeadLetterKey;
    const timestamp = Date.now();

    // Preparar dados para dead letter queue
    const deadLetterData = {
      ...emailData,
      _moved_to_dead_letter_at: new Date().toISOString(),
      _final_error: finalError,
      _total_attempts: emailData._retry_attempt || 1,
      _original_scheduled_date: emailData.scheduled_date,
    };

    // Salvar na dead letter queue do Redis
    const redisSuccess = await saveScheduledMessage(
      deadLetterKey,
      timestamp,
      `dead_${emailData.id}_${timestamp}`,
      deadLetterData
    );

    if (!redisSuccess) {
      console.error(`RETRYHANDLER > Falha ao salvar email ${emailData.id} na dead letter queue do Redis`);
      return false;
    }

    // Registrar no Firestore para auditoria
    await FirestoreRef.collection("email_dead_letter_queue")
      .doc(emailData.id)
      .set({
        ...deadLetterData,
        _firestore_saved_at: momentNow().format(CONSTANTS.MOMENT_ISO),
      });

    console.log(`RETRYHANDLER > Email ${emailData.id} movido para dead letter queue com sucesso`);
    return true;

  } catch (error) {
    console.error(`RETRYHANDLER > Erro ao mover email ${emailData.id} para dead letter queue:`, error.message);
    return false;
  }
};

/**
 * PROCESSAR DEAD LETTER QUEUE
 * Permite reprocessar emails da dead letter queue manualmente
 * 
 * @param {Object} options - Opções de processamento
 * @returns {Promise<Array>} Emails reprocessados
 */
const processDeadLetterQueue = async (options = {}) => {
  try {
    const {
      limit = 10,
      olderThan = 24 * 60 * 60 * 1000, // 24 horas em ms
      dryRun = true,
    } = options;

    console.log('RETRYHANDLER > Processando dead letter queue');
    console.log('RETRYHANDLER > Opções:', { limit, olderThan, dryRun });

    const { getScheduledMessages } = require("../utils/redisClient");
    const deadLetterKey = PERFORMANCE_CONFIG.redisDeadLetterKey;

    // Buscar emails antigos da dead letter queue
    const cutoffTimestamp = Date.now() - olderThan;
    const deadEmails = await getScheduledMessages(
      deadLetterKey,
      cutoffTimestamp,
      "DEAD_LETTER_PROCESS",
      { limit, remove: false }
    );

    if (deadEmails.length === 0) {
      console.log('RETRYHANDLER > Nenhum email encontrado na dead letter queue');
      return [];
    }

    console.log(`RETRYHANDLER > Encontrados ${deadEmails.length} emails na dead letter queue`);

    const reprocessedEmails = [];

    for (const email of deadEmails) {
      try {
        if (dryRun) {
          console.log(`RETRYHANDLER > [DRY RUN] Reprocessaria email ${email.id}`);
          reprocessedEmails.push({ ...email, status: 'dry_run_reprocess' });
        } else {
          // Reprocessar email (mover de volta para fila principal)
          const { addEmailToQueue } = require("./index");
          const success = await addEmailToQueue(email, new Date());

          if (success) {
            // Remover da dead letter queue
            const { removeMessage } = require("../utils/redisClient");
            await removeMessage(email.redis_key, deadLetterKey);

            console.log(`RETRYHANDLER > Email ${email.id} reprocessado com sucesso`);
            reprocessedEmails.push({ ...email, status: 'reprocessed' });
          } else {
            console.error(`RETRYHANDLER > Falha ao reprocessar email ${email.id}`);
            reprocessedEmails.push({ ...email, status: 'reprocess_failed' });
          }
        }
      } catch (error) {
        console.error(`RETRYHANDLER > Erro ao reprocessar email ${email.id}:`, error.message);
        reprocessedEmails.push({ ...email, status: 'error', error: error.message });
      }
    }

    return reprocessedEmails;

  } catch (error) {
    console.error('RETRYHANDLER > Erro ao processar dead letter queue:', error.message);
    return [];
  }
};

/**
 * OBTER MÉTRICAS DE RETRY E DEAD LETTER QUEUE
 * Fornece estatísticas sobre falhas e recuperação
 * 
 * @returns {Promise<Object>} Métricas de retry
 */
const getRetryMetrics = async () => {
  try {
    const { getRedisClient } = require("../utils/redisClient");
    const client = await getRedisClient();

    if (!client) {
      return { error: "Redis não disponível" };
    }

    const deadLetterCount = await client.zCard(PERFORMANCE_CONFIG.redisDeadLetterKey);
    
    // Buscar estatísticas do Firestore
    const deadLetterSnapshot = await FirestoreRef.collection("email_dead_letter_queue")
      .orderBy("_moved_to_dead_letter_at", "desc")
      .limit(100)
      .get();

    const processedSnapshot = await FirestoreRef.collection("email_queue_processed")
      .where("status", "in", ["retry_scheduled", "dead_letter"])
      .orderBy("_processed_at", "desc")
      .limit(100)
      .get();

    const metrics = {
      dead_letter_queue_size: deadLetterCount,
      recent_dead_letters: deadLetterSnapshot.size,
      recent_retries: processedSnapshot.docs.filter(doc => doc.data().status === "retry_scheduled").length,
      recent_dead_letter_moves: processedSnapshot.docs.filter(doc => doc.data().status === "dead_letter").length,
      timestamp: new Date().toISOString(),
    };

    console.log("RETRYHANDLER > METRICS >", metrics);
    return metrics;

  } catch (error) {
    console.error("RETRYHANDLER > METRICS > Erro:", error.message);
    return { error: error.message };
  }
};

/**
 * EXPORTS
 */
module.exports = {
  handleRetry,
  analyzeError,
  calculateRetryDelay,
  moveToDeadLetter,
  processDeadLetterQueue,
  getRetryMetrics,
};
