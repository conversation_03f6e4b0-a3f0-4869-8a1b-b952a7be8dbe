/**
 * SISTEMA DE FILAS REDIS PARA EMAILS - FUNÇÃO PRINCIPAL
 *
 * Este arquivo implementa o sistema de filas Redis para emails seguindo a arquitetura
 * moderna e segura do shotxCron. Substitui o processamento direto de emails por um
 * sistema baseado em filas com retry logic, dead letter queue e monitoramento.
 *
 * FUNÇÃO NO SISTEMA:
 * - Processar emails agendados da fila Redis
 * - Implementar retry logic com backoff exponencial
 * - Gerenciar dead letter queue para emails falhados
 * - Fornecer interface compatível com sistema atual
 * - Monitorar performance e métricas
 *
 * ARQUITETURA:
 * - emailQueue/index.js (este arquivo) - Função principal e orquestração
 * - emailQueue/emailProcessor.js - Processamento individual de emails
 * - emailQueue/providerManager.js - Gerenciamento de provedores (Mailgun/Resend/SMTP)
 * - emailQueue/retryHandler.js - Lógica de retry e dead letter queue
 */

const { FirestoreRef, CONSTANTS } = require("../init");
const { momentNow } = require("../helpers");
const {
  getScheduledMessages,
  removeMessage,
  saveScheduledMessage,
} = require("../utils/redisClient");
const { PERFORMANCE_CONFIG, REDIS_CONFIG } = require("../config/emailConfig");
const { validateCriticalConfigurations } = require("../config/securityConfig");
const { processEmail } = require("./emailProcessor");
const { handleRetry, moveToDeadLetter } = require("./retryHandler");

/**
 * FUNÇÃO PRINCIPAL PARA PROCESSAR EMAILS DA FILA REDIS
 * Equivalente ao shotxSendMessages para o sistema de emails
 *
 * @param {Object} options - Opções de processamento
 * @param {number} options.batchSize - Número máximo de emails a processar por vez
 * @param {boolean} options.dryRun - Se true, não envia emails, apenas simula
 * @param {boolean} options.validateConfig - Se true, valida configurações antes de processar
 * @returns {Promise<Array>} Lista de emails processados
 */
const emailQueueProcessor = async (options = {}) => {
  try {
    // Definir opções padrão
    const {
      batchSize = PERFORMANCE_CONFIG.batchSize,
      dryRun = false,
      validateConfig = true,
    } = options;

    console.log("EMAILQUEUE > PROCESSOR > Iniciando processamento de emails");
    console.log("EMAILQUEUE > PROCESSOR > Opções:", {
      batchSize,
      dryRun,
      validateConfig,
    });

    // Validar configurações se solicitado
    if (validateConfig) {
      try {
        validateCriticalConfigurations();
      } catch (error) {
        console.error(
          "EMAILQUEUE > PROCESSOR > Erro de configuração:",
          error.message
        );
        return [];
      }
    }

    // Verificar se Redis está habilitado
    if (!REDIS_CONFIG.useRedis) {
      console.log(
        "EMAILQUEUE > PROCESSOR > Redis desabilitado, usando processamento direto"
      );
      return await processEmailsDirect(options);
    }

    // Processar emails da fila Redis
    const processedEmails = await processScheduledEmails(options);

    console.log(
      `EMAILQUEUE > PROCESSOR > Processados ${processedEmails.length} emails`
    );

    return processedEmails;
  } catch (error) {
    console.error(
      "EMAILQUEUE > PROCESSOR > Erro no processamento:",
      error.message
    );
    console.error(error.stack);
    return [];
  }
};

/**
 * PROCESSAR EMAILS AGENDADOS DA FILA REDIS
 * Busca emails prontos para envio e os processa em lotes
 *
 * @param {Object} options - Opções de processamento
 * @returns {Promise<Array>} Lista de emails processados
 */
const processScheduledEmails = async (options = {}) => {
  const { batchSize = PERFORMANCE_CONFIG.batchSize, dryRun = false } = options;

  try {
    // Chave da lista ordenada de emails agendados
    const scheduledListKey = PERFORMANCE_CONFIG.redisQueueKey;

    // Timestamp atual para buscar emails prontos para envio
    const momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);
    const momentNowTimestamp = new Date(momentNowISO).getTime();

    console.log("EMAILQUEUE > PROCESS > Buscando emails agendados...");
    console.log("EMAILQUEUE > PROCESS > Timestamp atual:", momentNowTimestamp);

    // Obter emails agendados do Redis
    const emails = await getScheduledMessages(
      scheduledListKey,
      momentNowTimestamp,
      "PROCESS",
      {
        limit: batchSize,
        remove: false, // Não remover ainda, remover após processamento bem-sucedido
      }
    );

    if (emails.length === 0) {
      console.log("EMAILQUEUE > PROCESS > Nenhum email agendado encontrado");
      return [];
    }

    const totalEmails = emails.length;
    console.log(
      `EMAILQUEUE > PROCESS > Encontrados ${totalEmails} emails para processar`
    );

    // Processar emails em paralelo para melhor performance
    const emailsProcessed = [];
    const emailPromises = emails.map(async (email, index) => {
      try {
        const emailId =
          email.id ||
          `email_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

        console.log(
          `EMAILQUEUE > PROCESS > Processando email ${index + 1}/${totalEmails}: ${emailId}`
        );

        // Registrar o email no Firestore antes de processar
        await FirestoreRef.collection("email_queue_processed")
          .doc(emailId)
          .set({
            ...email,
            _processing_started_at: momentNow().format(CONSTANTS.MOMENT_ISO),
            status: "processing",
            _dry_run: dryRun,
          });

        // Se for modo de simulação, não enviar realmente
        if (dryRun) {
          console.log(
            `EMAILQUEUE > PROCESS > [DRY RUN] Simulando envio do email ${emailId}`
          );

          // Atualizar status no Firestore
          await FirestoreRef.collection("email_queue_processed")
            .doc(emailId)
            .update({
              status: "simulated",
              _processed_at: momentNow().format(CONSTANTS.MOMENT_ISO),
            });

          // Remover da fila Redis (simulação bem-sucedida)
          if (email.redis_key) {
            await removeMessage(email.redis_key, scheduledListKey);
          }

          return { ...email, id: emailId, status: "simulated" };
        }

        // Processar email real
        const result = await processEmail(email);

        if (result.success) {
          console.log(
            `EMAILQUEUE > PROCESS > Email ${emailId} enviado com sucesso`
          );

          // Atualizar status no Firestore
          await FirestoreRef.collection("email_queue_processed")
            .doc(emailId)
            .update({
              status: "sent",
              _processed_at: momentNow().format(CONSTANTS.MOMENT_ISO),
              _result: result,
            });

          // Remover da fila Redis (envio bem-sucedido)
          if (email.redis_key) {
            await removeMessage(email.redis_key, scheduledListKey);
          }

          emailsProcessed.push({
            ...email,
            id: emailId,
            status: "sent",
            result,
          });
        } else {
          console.error(
            `EMAILQUEUE > PROCESS > Falha ao enviar email ${emailId}:`,
            result.error
          );

          // Tentar retry ou mover para dead letter queue
          const retryResult = await handleRetry(email, result.error);

          if (retryResult.shouldRetry) {
            console.log(
              `EMAILQUEUE > PROCESS > Email ${emailId} será tentado novamente em ${retryResult.retryDelay}ms`
            );

            // Atualizar status no Firestore
            await FirestoreRef.collection("email_queue_processed")
              .doc(emailId)
              .update({
                status: "retry_scheduled",
                _processed_at: momentNow().format(CONSTANTS.MOMENT_ISO),
                _retry_attempt: retryResult.attempt,
                _retry_delay: retryResult.retryDelay,
                _last_error: result.error,
              });

            // Reagendar na fila Redis
            const retryTimestamp = Date.now() + retryResult.retryDelay;
            const retryEmailId = `${emailId}_retry_${retryResult.attempt}`;
            await saveScheduledMessage(
              scheduledListKey,
              retryTimestamp,
              retryEmailId,
              { ...email, id: emailId, _retry_attempt: retryResult.attempt }
            );

            // Remover email original da fila
            if (email.redis_key) {
              await removeMessage(email.redis_key, scheduledListKey);
            }
          } else {
            console.error(
              `EMAILQUEUE > PROCESS > Email ${emailId} movido para dead letter queue`
            );

            // Mover para dead letter queue
            await moveToDeadLetter(email, result.error);

            // Atualizar status no Firestore
            await FirestoreRef.collection("email_queue_processed")
              .doc(emailId)
              .update({
                status: "dead_letter",
                _processed_at: momentNow().format(CONSTANTS.MOMENT_ISO),
                _final_error: result.error,
              });

            // Remover da fila principal
            if (email.redis_key) {
              await removeMessage(email.redis_key, scheduledListKey);
            }
          }

          emailsProcessed.push({
            ...email,
            id: emailId,
            status: retryResult.shouldRetry ? "retry_scheduled" : "dead_letter",
            error: result.error,
          });
        }

        return { ...email, id: emailId };
      } catch (error) {
        console.error(
          `EMAILQUEUE > PROCESS > Erro ao processar email ${index + 1}:`,
          error.message
        );
        return { ...email, status: "error", error: error.message };
      }
    });

    // Aguardar processamento de todos os emails
    await Promise.all(emailPromises);

    console.log(
      `EMAILQUEUE > PROCESS > Processamento concluído: ${emailsProcessed.length} emails processados`
    );

    return emailsProcessed;
  } catch (error) {
    console.error(
      "EMAILQUEUE > PROCESS > Erro no processamento:",
      error.message
    );
    return [];
  }
};

/**
 * PROCESSAMENTO DIRETO DE EMAILS (FALLBACK)
 * Usado quando Redis está desabilitado, mantém compatibilidade com sistema atual
 *
 * @param {Object} options - Opções de processamento
 * @returns {Promise<Array>} Lista de emails processados
 */
const processEmailsDirect = async (options = {}) => {
  console.log(
    "EMAILQUEUE > DIRECT > Processamento direto de emails (fallback)"
  );

  // Implementar lógica de fallback que usa o sistema atual
  // Por enquanto, retornar array vazio
  // TODO: Implementar integração com sistema atual se necessário

  return [];
};

/**
 * ADICIONAR EMAIL À FILA REDIS
 * Interface para adicionar novos emails à fila de processamento
 *
 * @param {Object} emailData - Dados do email
 * @param {Date|string} scheduledDate - Data de agendamento
 * @returns {Promise<boolean>} Sucesso da operação
 */
const addEmailToQueue = async (emailData, scheduledDate = null) => {
  try {
    const scheduledTimestamp = scheduledDate
      ? new Date(scheduledDate).getTime()
      : Date.now();

    const emailId = `email_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

    const emailWithMetadata = {
      ...emailData,
      id: emailId,
      created_at: new Date().toISOString(),
      scheduled_date: new Date(scheduledTimestamp).toISOString(),
      attempts: 0,
    };

    const success = await saveScheduledMessage(
      PERFORMANCE_CONFIG.redisQueueKey,
      scheduledTimestamp,
      emailId,
      emailWithMetadata
    );

    if (success) {
      console.log(`EMAILQUEUE > ADD > Email ${emailId} adicionado à fila`);
    } else {
      console.error(
        `EMAILQUEUE > ADD > Falha ao adicionar email ${emailId} à fila`
      );
    }

    return success;
  } catch (error) {
    console.error(
      "EMAILQUEUE > ADD > Erro ao adicionar email à fila:",
      error.message
    );
    return false;
  }
};

/**
 * OBTER MÉTRICAS DA FILA DE EMAILS
 * Fornece estatísticas sobre o estado atual da fila
 *
 * @returns {Promise<Object>} Métricas da fila
 */
const getEmailQueueMetrics = async () => {
  try {
    const { getRedisClient } = require("../utils/redisClient");
    const client = await getRedisClient();

    if (!client) {
      return { error: "Redis não disponível" };
    }

    const scheduledCount = await client.zCard(PERFORMANCE_CONFIG.redisQueueKey);
    const deadLetterCount = await client.zCard(
      PERFORMANCE_CONFIG.redisDeadLetterKey
    );
    const processingCount = await client.zCard(
      PERFORMANCE_CONFIG.redisProcessingKey
    );

    const metrics = {
      scheduled_emails: scheduledCount,
      dead_letter_emails: deadLetterCount,
      processing_emails: processingCount,
      total_emails: scheduledCount + deadLetterCount + processingCount,
      timestamp: new Date().toISOString(),
    };

    console.log("EMAILQUEUE > METRICS >", metrics);
    return metrics;
  } catch (error) {
    console.error("EMAILQUEUE > METRICS > Erro:", error.message);
    return { error: error.message };
  }
};

/**
 * EXPORTS
 */
module.exports = {
  // Função principal
  emailQueueProcessor,

  // Funções auxiliares
  processScheduledEmails,
  processEmailsDirect,
  addEmailToQueue,
  getEmailQueueMetrics,

  // Compatibilidade com sistema atual
  emailCron: emailQueueProcessor, // Alias para compatibilidade
};
