{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"lint": "eslint .", "serve": "firebase serve --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "dependencies": {"@firebase/app": "^0.7.2", "axios": "^0.22.0", "dotenv": "^16.5.0", "facebook-nodejs-business-sdk": "^11.0.0", "fast-geoip": "^1.1.13", "firebase": "^9.1.1", "firebase-admin": "^9.10.0", "firebase-functions": "^3.16.0", "imap": "^0.8.19", "mailgun-js": "^0.22.0", "mailparser": "^3.3.3", "moment": "^2.24.0", "nodemailer": "^6.6.5", "pagarme": "^4.12.0", "qs": "^6.9.6", "quoted-printable": "^1.0.1", "react-native-firebase-upload": "^0.0.14", "redis": "^5.0.1", "resend": "^4.4.1", "ua-parser-js": "^0.7.27"}, "devDependencies": {"eslint": "^5.12.0", "eslint-plugin-promise": "^4.0.1", "firebase-functions-test": "^0.1.6"}, "private": true}