/**
 * CONFIGURAÇÃO DE SEGURANÇA PARA SISTEMA DE EMAILS
 * 
 * Este arquivo implementa validações de segurança e auditoria para o sistema de emails.
 * Garante que nenhuma credencial sensível seja exposta e que todas as configurações
 * estejam devidamente protegidas.
 * 
 * FUNÇÃO NO SISTEMA:
 * - Validar configurações de segurança na inicialização
 * - Detectar credenciais hardcoded ou expostas
 * - Implementar políticas de segurança para credenciais
 * - Fornecer auditoria e logging de segurança
 */

const functions = require("firebase-functions");
const fs = require("fs");
const path = require("path");

/**
 * LISTA DE CREDENCIAIS CONHECIDAS QUE DEVEM SER REMOVIDAS
 * Estas são as credenciais hardcoded identificadas na auditoria
 */
const KNOWN_EXPOSED_CREDENTIALS = [
  '**************************************************', // Mailgun API Key
  'pubkey-b2e168cc42dfedf46b3aa9d752ecec1e', // Mailgun Public Key
  'QIPlus@Amg!', // Mailgun Password
  'Fck123@Amg!', // SMTP Admin Password
  'ak_test_9KRGIevO33Bo02hv4yBoXgrYZWCco2', // PagarMe Test
  '**************************************', // PagarMe Live
  'ek_test_EUETAbO0mPQmo2A0eqaFW34cQGYF4c', // PagarMe Encrypt Test
  'ek_live_01X4Qnn0hGs0LKRJcvew2W893X7Oze', // PagarMe Encrypt Live
  '78e9032a-cef5-4aaf-9459-da7a3d66decc', // Chat Key
  '4c3e570bf51e18f15bb4d0a8a68939d7cd438128', // Bitly Token
];

/**
 * PADRÕES DE CREDENCIAIS SENSÍVEIS
 * Regex patterns para detectar possíveis credenciais
 */
const CREDENTIAL_PATTERNS = [
  /api[_-]?key['":\s]*['"]\w{20,}['"]/, // API Keys
  /secret['":\s]*['"]\w{20,}['"]/, // Secrets
  /password['":\s]*['"]\w{8,}['"]/, // Passwords
  /token['":\s]*['"]\w{20,}['"]/, // Tokens
  /key['":\s]*['"][a-zA-Z0-9_-]{20,}['"]/, // Generic keys
  /['"]\w*[Kk]ey['":\s]*['"]\w{20,}['"]/, // Keys with Key suffix
];

/**
 * VALIDAÇÃO DE CONFIGURAÇÕES CRÍTICAS
 * Verifica se todas as configurações necessárias estão presentes e seguras
 */
const validateCriticalConfigurations = () => {
  const errors = [];
  const warnings = [];
  
  console.log('🔍 SECURITYCONFIG > Iniciando validação de configurações críticas...');
  
  // Verificar configurações do Firebase Functions
  const config = functions.config();
  
  // Validar Mailgun
  if (!config.mailgun?.api_key) {
    errors.push('❌ Mailgun API Key não configurada (firebase functions:config:set mailgun.api_key="...")');
  }
  if (!config.mailgun?.public_key) {
    errors.push('❌ Mailgun Public Key não configurada (firebase functions:config:set mailgun.public_key="...")');
  }
  if (!config.mailgun?.password) {
    warnings.push('⚠️ Mailgun Password não configurada (opcional para alguns casos)');
  }
  
  // Validar Resend
  if (!config.resend?.apikey) {
    errors.push('❌ Resend API Key não configurada (firebase functions:config:set resend.apikey="...")');
  }
  
  // Validar SMTP
  if (!config.smtp?.admin_pass) {
    errors.push('❌ SMTP Admin Password não configurada (firebase functions:config:set smtp.admin_pass="...")');
  }
  
  // Validar Redis
  if (!config.redis?.host) {
    errors.push('❌ Redis Host não configurado (firebase functions:config:set redis.host="...")');
  }
  if (!config.redis?.password) {
    warnings.push('⚠️ Redis Password não configurada (recomendado para produção)');
  }
  
  // Validar PagarMe (se usado)
  if (!config.pagarme?.api_key_live && !config.pagarme?.api_key_test) {
    warnings.push('⚠️ PagarMe API Keys não configuradas (necessário se usar pagamentos)');
  }
  
  // Exibir resultados
  if (errors.length > 0) {
    console.error('🚨 SECURITYCONFIG > CONFIGURAÇÕES CRÍTICAS FALTANDO:');
    errors.forEach(error => console.error(error));
    throw new Error(`Configurações críticas faltando: ${errors.length} erros encontrados`);
  }
  
  if (warnings.length > 0) {
    console.warn('⚠️ SECURITYCONFIG > AVISOS DE CONFIGURAÇÃO:');
    warnings.forEach(warning => console.warn(warning));
  }
  
  console.log('✅ SECURITYCONFIG > Todas as configurações críticas validadas');
  return { errors, warnings };
};

/**
 * SCAN DE CREDENCIAIS HARDCODED
 * Verifica se ainda existem credenciais expostas no código
 */
const scanForHardcodedCredentials = (directory = path.join(__dirname, '..')) => {
  const findings = [];
  
  console.log('🔍 SECURITYCONFIG > Iniciando scan de credenciais hardcoded...');
  
  const scanFile = (filePath) => {
    try {
      // Pular arquivos que não devem ser escaneados
      if (filePath.includes('node_modules') || 
          filePath.includes('.git') || 
          filePath.endsWith('.log') ||
          filePath.endsWith('.md')) {
        return;
      }
      
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Verificar credenciais conhecidas expostas
      KNOWN_EXPOSED_CREDENTIALS.forEach(credential => {
        if (content.includes(credential)) {
          findings.push({
            type: 'KNOWN_EXPOSED_CREDENTIAL',
            file: filePath,
            credential: credential.substring(0, 10) + '...' + credential.substring(credential.length - 4),
            severity: 'CRITICAL'
          });
        }
      });
      
      // Verificar padrões de credenciais
      CREDENTIAL_PATTERNS.forEach((pattern, index) => {
        const matches = content.match(pattern);
        if (matches) {
          matches.forEach(match => {
            findings.push({
              type: 'POTENTIAL_CREDENTIAL',
              file: filePath,
              pattern: `Pattern ${index + 1}`,
              match: match.substring(0, 30) + '...',
              severity: 'HIGH'
            });
          });
        }
      });
      
    } catch (error) {
      // Ignorar erros de leitura de arquivo (permissões, etc.)
    }
  };
  
  const scanDirectory = (dir) => {
    try {
      const items = fs.readdirSync(dir);
      
      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          scanDirectory(fullPath);
        } else if (stat.isFile() && (
          fullPath.endsWith('.js') || 
          fullPath.endsWith('.json') || 
          fullPath.endsWith('.env')
        )) {
          scanFile(fullPath);
        }
      });
    } catch (error) {
      // Ignorar erros de leitura de diretório
    }
  };
  
  scanDirectory(directory);
  
  // Exibir resultados
  if (findings.length > 0) {
    console.error('🚨 SECURITYCONFIG > CREDENCIAIS EXPOSTAS ENCONTRADAS:');
    findings.forEach(finding => {
      console.error(`${finding.severity === 'CRITICAL' ? '🔴' : '🟡'} ${finding.type}: ${finding.file}`);
      if (finding.credential) {
        console.error(`   Credencial: ${finding.credential}`);
      }
      if (finding.match) {
        console.error(`   Match: ${finding.match}`);
      }
    });
    
    if (findings.some(f => f.severity === 'CRITICAL')) {
      throw new Error(`CREDENCIAIS CRÍTICAS EXPOSTAS ENCONTRADAS! ${findings.filter(f => f.severity === 'CRITICAL').length} credenciais críticas detectadas.`);
    }
  } else {
    console.log('✅ SECURITYCONFIG > Nenhuma credencial hardcoded encontrada');
  }
  
  return findings;
};

/**
 * VALIDAÇÃO DE POLÍTICAS DE SEGURANÇA
 * Verifica se as políticas de segurança estão sendo seguidas
 */
const validateSecurityPolicies = () => {
  const policies = [];
  
  console.log('🔍 SECURITYCONFIG > Validando políticas de segurança...');
  
  // Política 1: Arquivo .env deve existir e estar no .gitignore
  const envExists = fs.existsSync(path.join(__dirname, '..', '.env'));
  const gitignoreExists = fs.existsSync(path.join(__dirname, '..', '.gitignore'));
  
  if (!envExists) {
    policies.push({
      policy: 'ENV_FILE_EXISTS',
      status: 'FAIL',
      message: 'Arquivo .env não encontrado'
    });
  } else {
    policies.push({
      policy: 'ENV_FILE_EXISTS',
      status: 'PASS',
      message: 'Arquivo .env encontrado'
    });
  }
  
  if (gitignoreExists) {
    const gitignoreContent = fs.readFileSync(path.join(__dirname, '..', '.gitignore'), 'utf8');
    if (gitignoreContent.includes('.env')) {
      policies.push({
        policy: 'ENV_IN_GITIGNORE',
        status: 'PASS',
        message: '.env está no .gitignore'
      });
    } else {
      policies.push({
        policy: 'ENV_IN_GITIGNORE',
        status: 'FAIL',
        message: '.env NÃO está no .gitignore'
      });
    }
  }
  
  // Política 2: Configurações via Firebase Functions config
  const config = functions.config();
  const hasFirebaseConfig = Object.keys(config).length > 0;
  
  policies.push({
    policy: 'FIREBASE_CONFIG_USED',
    status: hasFirebaseConfig ? 'PASS' : 'FAIL',
    message: hasFirebaseConfig ? 'Firebase Functions config em uso' : 'Firebase Functions config não configurado'
  });
  
  // Exibir resultados
  const failedPolicies = policies.filter(p => p.status === 'FAIL');
  
  if (failedPolicies.length > 0) {
    console.error('🚨 SECURITYCONFIG > POLÍTICAS DE SEGURANÇA FALHARAM:');
    failedPolicies.forEach(policy => {
      console.error(`❌ ${policy.policy}: ${policy.message}`);
    });
  }
  
  const passedPolicies = policies.filter(p => p.status === 'PASS');
  console.log('✅ SECURITYCONFIG > POLÍTICAS APROVADAS:');
  passedPolicies.forEach(policy => {
    console.log(`✅ ${policy.policy}: ${policy.message}`);
  });
  
  return policies;
};

/**
 * AUDITORIA COMPLETA DE SEGURANÇA
 * Executa todas as validações de segurança
 */
const performSecurityAudit = () => {
  console.log('🔒 SECURITYCONFIG > Iniciando auditoria completa de segurança...');
  
  const results = {
    timestamp: new Date().toISOString(),
    configurations: null,
    credentialScan: null,
    policies: null,
    overall: 'UNKNOWN'
  };
  
  try {
    // 1. Validar configurações críticas
    results.configurations = validateCriticalConfigurations();
    
    // 2. Scan de credenciais hardcoded
    results.credentialScan = scanForHardcodedCredentials();
    
    // 3. Validar políticas de segurança
    results.policies = validateSecurityPolicies();
    
    // Determinar status geral
    const hasCriticalIssues = 
      results.configurations.errors.length > 0 ||
      results.credentialScan.some(f => f.severity === 'CRITICAL') ||
      results.policies.some(p => p.status === 'FAIL' && p.policy.includes('CRITICAL'));
    
    results.overall = hasCriticalIssues ? 'FAIL' : 'PASS';
    
    if (results.overall === 'PASS') {
      console.log('🎉 SECURITYCONFIG > AUDITORIA DE SEGURANÇA APROVADA!');
    } else {
      console.error('🚨 SECURITYCONFIG > AUDITORIA DE SEGURANÇA FALHOU!');
    }
    
  } catch (error) {
    console.error('💥 SECURITYCONFIG > Erro durante auditoria de segurança:', error.message);
    results.overall = 'ERROR';
    results.error = error.message;
  }
  
  return results;
};

/**
 * EXPORTS
 */
module.exports = {
  validateCriticalConfigurations,
  scanForHardcodedCredentials,
  validateSecurityPolicies,
  performSecurityAudit,
  
  // Constantes para referência
  KNOWN_EXPOSED_CREDENTIALS,
  CREDENTIAL_PATTERNS,
};
