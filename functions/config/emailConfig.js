/**
 * CONFIGURAÇÃO CENTRALIZADA E SEGURA PARA SISTEMA DE EMAILS
 * 
 * Este arquivo centraliza todas as configurações de email seguindo padrões de segurança.
 * Substitui as credenciais hardcoded por configurações via Firebase Functions config e .env
 * 
 * FUNÇÃO NO SISTEMA:
 * - Centralizar configurações de todos os provedores de email
 * - Validar credenciais na inicialização
 * - Fornecer interface unificada para acesso a configurações
 * - Implementar fallbacks seguros para configurações opcionais
 */

const functions = require("firebase-functions");
const { CONSTANTS } = require("../init");
const dotenv = require("dotenv");

// Carregar variáveis de ambiente
dotenv.config();

/**
 * CONFIGURAÇÕES DE PROVEDORES DE EMAIL
 * Todas as credenciais sensíveis vêm do Firebase Functions config
 * Configurações não-sensíveis vêm de variáveis de ambiente
 */

// ✅ MAILGUN - Configuração segura
const MAILGUN_CONFIG = {
  // Credenciais sensíveis via Firebase Functions config
  apiKey: functions.config().mailgun?.api_key,
  publicKey: functions.config().mailgun?.public_key,
  password: functions.config().mailgun?.password,
  
  // Configurações não-sensíveis via environment variables
  emailAppDomain: process.env.EMAIL_APP_DOMAIN || 'qiplus.cloud',
  mailingDomain: process.env.MAILING_DOMAIN || 'mail.qiplus.com.br',
  devMailingDomain: process.env.DEV_MAILING_DOMAIN || 'devmail.qiplus.com.br',
  scheduleLimitDays: parseInt(process.env.MAILGUN_SCHEDULE_LIMIT_DAYS || '3'),
  
  // URLs de endpoints
  messagesEndpoint: `${CONSTANTS.REMOTE_URL}/mail/messages.php`,
  storageEndpoint: `${CONSTANTS.REMOTE_URL}/mail/store.php`,
  eventsApiURL: 'https://api.mailgun.net/v3/events/',
};

// Domínio padrão baseado no ambiente
MAILGUN_CONFIG.defaultDomain = CONSTANTS.APP_ENV === CONSTANTS.DEV_ENV 
  ? MAILGUN_CONFIG.devMailingDomain 
  : MAILGUN_CONFIG.mailingDomain;

MAILGUN_CONFIG.defaultFromEmail = `mailing@${MAILGUN_CONFIG.defaultDomain}`;
MAILGUN_CONFIG.qiplusDomains = [
  MAILGUN_CONFIG.emailAppDomain, 
  MAILGUN_CONFIG.mailingDomain, 
  MAILGUN_CONFIG.devMailingDomain
];

// ✅ RESEND - Configuração segura (já estava correta)
const RESEND_CONFIG = {
  apiKey: functions.config().resend?.apikey,
  
  // Reutilizar domínios do Mailgun para compatibilidade
  emailAppDomain: MAILGUN_CONFIG.emailAppDomain,
  mailingDomain: MAILGUN_CONFIG.mailingDomain,
  devMailingDomain: MAILGUN_CONFIG.devMailingDomain,
  defaultDomain: MAILGUN_CONFIG.defaultDomain,
  defaultFromEmail: MAILGUN_CONFIG.defaultFromEmail,
  qiplusDomains: MAILGUN_CONFIG.qiplusDomains,
  
  // Endpoints
  messagesEndpoint: MAILGUN_CONFIG.messagesEndpoint,
  storageEndpoint: MAILGUN_CONFIG.storageEndpoint,
};

// ✅ SMTP - Configuração segura
const SMTP_CONFIG = {
  // Credenciais sensíveis via Firebase Functions config
  adminUser: functions.config().smtp?.admin_user || process.env.SMTP_ADMIN_USER,
  adminPass: functions.config().smtp?.admin_pass,
  
  // Configurações não-sensíveis via environment variables
  host: process.env.SMTP_HOST || 'smtp.zoho.com',
  port: parseInt(process.env.SMTP_PORT || '465'),
  encryption: process.env.SMTP_ENCRYPTION || 'ssl',
  name: process.env.SMTP_ADMIN_NAME || 'Admin || QIPlus',
  service: process.env.SMTP_SERVICE || '', // Para serviços como Gmail, Yahoo, etc.
};

// ✅ CONFIGURAÇÕES DE PERFORMANCE E RETRY
const PERFORMANCE_CONFIG = {
  // Configurações de batch e timeout
  batchSize: parseInt(process.env.EMAIL_BATCH_SIZE || '50'),
  timeoutSeconds: parseInt(process.env.EMAIL_TIMEOUT_SECONDS || '30'),
  
  // Configurações de retry
  retryAttempts: parseInt(process.env.EMAIL_RETRY_ATTEMPTS || '3'),
  retryDelayMs: parseInt(process.env.EMAIL_RETRY_DELAY_MS || '1000'),
  retryBackoffMultiplier: parseFloat(process.env.EMAIL_RETRY_BACKOFF_MULTIPLIER || '2'),
  maxRetryDelayMs: parseInt(process.env.EMAIL_MAX_RETRY_DELAY_MS || '30000'),
  
  // Configurações de fila Redis
  redisQueueKey: process.env.EMAIL_REDIS_QUEUE_KEY || 'email:scheduled_emails',
  redisDeadLetterKey: process.env.EMAIL_REDIS_DEAD_LETTER_KEY || 'email:dead_letter_queue',
  redisProcessingKey: process.env.EMAIL_REDIS_PROCESSING_KEY || 'email:processing',
  
  // Configurações de monitoramento
  metricsEnabled: process.env.EMAIL_METRICS_ENABLED === 'true',
  loggingLevel: process.env.EMAIL_LOGGING_LEVEL || 'info', // debug, info, warn, error
};

// ✅ CONFIGURAÇÕES DE REDIS (seguindo padrão do shotxCron)
const REDIS_CONFIG = {
  useRedis: process.env.EMAIL_USE_REDIS !== 'false', // Habilitado por padrão
  host: functions.config().redis?.host || process.env.REDIS_HOST || 'localhost',
  port: parseInt(functions.config().redis?.port || process.env.REDIS_PORT || '6379'),
  password: functions.config().redis?.password || process.env.REDIS_PASSWORD || '',
  database: parseInt(functions.config().redis?.database || process.env.REDIS_DATABASE || '0'),
  timeout: parseInt(process.env.REDIS_TIMEOUT || '10000'),
};

/**
 * VALIDAÇÃO DE CONFIGURAÇÕES CRÍTICAS
 * Verifica se todas as credenciais necessárias estão configuradas
 */
const validateEmailConfig = () => {
  const errors = [];
  
  // Validar Mailgun
  if (!MAILGUN_CONFIG.apiKey) {
    errors.push('Mailgun API Key não configurada (mailgun.api_key)');
  }
  if (!MAILGUN_CONFIG.publicKey) {
    errors.push('Mailgun Public Key não configurada (mailgun.public_key)');
  }
  
  // Validar Resend
  if (!RESEND_CONFIG.apiKey) {
    errors.push('Resend API Key não configurada (resend.apikey)');
  }
  
  // Validar SMTP
  if (!SMTP_CONFIG.adminPass) {
    errors.push('SMTP Admin Password não configurada (smtp.admin_pass)');
  }
  
  // Validar Redis (se habilitado)
  if (REDIS_CONFIG.useRedis && !REDIS_CONFIG.host) {
    errors.push('Redis Host não configurado (redis.host)');
  }
  
  if (errors.length > 0) {
    const errorMessage = `❌ CONFIGURAÇÕES DE EMAIL INVÁLIDAS:\n${errors.join('\n')}`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }
  
  console.log('✅ EMAILCONFIG > Todas as configurações de email validadas com sucesso');
  return true;
};

/**
 * TESTE DE CONECTIVIDADE COM PROVEDORES
 * Testa se as credenciais estão funcionando
 */
const testProviderConnectivity = async () => {
  const results = {
    mailgun: false,
    resend: false,
    smtp: false,
    redis: false
  };
  
  try {
    // Teste Mailgun (verificação simples de domínio)
    if (MAILGUN_CONFIG.apiKey) {
      const mailgunjs = require("mailgun-js");
      const mg = mailgunjs({
        apiKey: MAILGUN_CONFIG.apiKey,
        domain: MAILGUN_CONFIG.defaultDomain
      });
      // Teste simples - listar domínios
      await mg.domains().list();
      results.mailgun = true;
      console.log('✅ EMAILCONFIG > Mailgun conectado com sucesso');
    }
  } catch (error) {
    console.error('❌ EMAILCONFIG > Erro ao conectar com Mailgun:', error.message);
  }
  
  try {
    // Teste Resend
    if (RESEND_CONFIG.apiKey) {
      const { Resend } = require("resend");
      const resend = new Resend(RESEND_CONFIG.apiKey);
      // Teste simples - listar domínios
      await resend.domains.list();
      results.resend = true;
      console.log('✅ EMAILCONFIG > Resend conectado com sucesso');
    }
  } catch (error) {
    console.error('❌ EMAILCONFIG > Erro ao conectar com Resend:', error.message);
  }
  
  try {
    // Teste Redis
    if (REDIS_CONFIG.useRedis) {
      const { getRedisClient } = require("../utils/redisClient");
      const client = await getRedisClient();
      if (client && client.isOpen) {
        results.redis = true;
        console.log('✅ EMAILCONFIG > Redis conectado com sucesso');
      }
    }
  } catch (error) {
    console.error('❌ EMAILCONFIG > Erro ao conectar com Redis:', error.message);
  }
  
  return results;
};

/**
 * OBTER CONFIGURAÇÃO DE PROVEDOR ESPECÍFICO
 */
const getProviderConfig = (provider) => {
  switch (provider.toLowerCase()) {
    case 'mailgun':
      return MAILGUN_CONFIG;
    case 'resend':
      return RESEND_CONFIG;
    case 'smtp':
      return SMTP_CONFIG;
    default:
      throw new Error(`Provedor de email não suportado: ${provider}`);
  }
};

/**
 * EXPORTS
 */
module.exports = {
  // Configurações por provedor
  MAILGUN_CONFIG,
  RESEND_CONFIG,
  SMTP_CONFIG,
  
  // Configurações gerais
  PERFORMANCE_CONFIG,
  REDIS_CONFIG,
  
  // Funções utilitárias
  validateEmailConfig,
  testProviderConnectivity,
  getProviderConfig,
  
  // Compatibilidade com código existente (deprecated - usar as novas configurações)
  EMAIL_APP_DOMAIN: MAILGUN_CONFIG.emailAppDomain,
  MAILING_DOMAIN: MAILGUN_CONFIG.mailingDomain,
  DEV_MAILING_DOMAIN: MAILGUN_CONFIG.devMailingDomain,
  DEFAULT_MAILING_DOMAIN: MAILGUN_CONFIG.defaultDomain,
  DEFAULT_FROM_EMAIL: MAILGUN_CONFIG.defaultFromEmail,
  qiplusDomains: MAILGUN_CONFIG.qiplusDomains,
  MAILGUN_API_KEY: MAILGUN_CONFIG.apiKey,
  MAILGUN_PUB_KEY: MAILGUN_CONFIG.publicKey,
  MAILGUN_SCHEDULE_LIMIT_DAYS: MAILGUN_CONFIG.scheduleLimitDays,
  MAILGUN_SCHEDULE_LIMIT_TIME: MAILGUN_CONFIG.scheduleLimitDays * 24 * 60 * 60 * 1000,
  MESSAGES_ENDPOINT: MAILGUN_CONFIG.messagesEndpoint,
  STORAGE_ENDPOINT: MAILGUN_CONFIG.storageEndpoint,
};
