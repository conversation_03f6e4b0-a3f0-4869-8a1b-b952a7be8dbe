const CONSTANTS = require("../constants");
const { default: Axios } = require("axios");

const getServerToken = () => {
  return CONSTANTS.CHAT_KEY;
};

const sendMessage = (message, number, account, instance) => {
  const token = getServerToken();

  const data = {
    accountId: account,
    userId: account,
    phone: number,
    message: message,
  };

  console.log("SENDING MESSAGE SHOTX", data, instance);

  new Promise((resolve, reject) => {
    let config = {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    };

    Axios.post(
      `${process.env.CHAT_API_URL}/whatsapp/message/send/${instance}`,
      JSON.stringify(data),
      config
    );
  });
};

module.exports = {
  sendMessage,
};
