{"indexes": [{"collectionGroup": "accounts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "shotx-cron", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "scheduled_date", "order": "ASCENDING"}]}, {"collectionGroup": "shotx-cron", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "schedule_date", "order": "ASCENDING"}]}, {"collectionGroup": "shotx-cron", "fields": [{"fieldPath": "sent", "order": "ASCENDING"}, {"fieldPath": "scheduled_date", "order": "ASCENDING"}]}, {"collectionGroup": "stories", "fields": [{"fieldPath": "relation", "order": "ASCENDING"}, {"fieldPath": "relationId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "ASCENDING"}]}, {"collectionGroup": "sessions", "fields": [{"fieldPath": "contactId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}]}, {"collectionGroup": "messages", "fields": [{"fieldPath": "sender", "order": "ASCENDING"}, {"fieldPath": "shotxCron.replied", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "ASCENDING"}]}, {"collectionGroup": "shotx-cron", "fields": [{"fieldPath": "executed", "order": "ASCENDING"}, {"fieldPath": "scheduled_date", "order": "ASCENDING"}]}, {"collectionGroup": "messages", "fields": [{"fieldPath": "sender", "order": "ASCENDING"}, {"fieldPath": "shotxCron.replied", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "leads", "fields": [{"fieldPath": "ig_sids", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "leads", "fields": [{"fieldPath": "ID", "order": "ASCENDING"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "messages", "fields": [{"fieldPath": "sender", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "leads", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "email", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "leads", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "leads", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "ID", "order": "ASCENDING"}]}, {"collectionGroup": "leads", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "mobile", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "integrations", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "shotx-cron", "fields": [{"fieldPath": "sent", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "scheduled_date", "order": "ASCENDING"}]}, {"collectionGroup": "hunters", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "fields": [{"fieldPath": "sender", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "leads", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "phone", "order": "ASCENDING"}, {"fieldPath": "phoneCC", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "leads", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "address.postalCode", "order": "ASCENDING"}]}, {"collectionGroup": "leads", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "leads", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "ig_sids", "arrayConfig": "CONTAINS"}]}, {"collectionGroup": "leads", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "mobile", "order": "ASCENDING"}, {"fieldPath": "mobileCC", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "leads", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "birthday", "order": "ASCENDING"}]}, {"collectionGroup": "shotx-cron", "fields": [{"fieldPath": "executed", "order": "ASCENDING"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "scheduled_date", "order": "ASCENDING"}]}, {"collectionGroup": "shotx-cron", "fields": [{"fieldPath": "instance.accountId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "shotx-cron", "fields": [{"fieldPath": "shotxCron.replied", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "ASCENDING"}]}, {"collectionGroup": "stories", "fields": [{"fieldPath": "relation", "order": "ASCENDING"}, {"fieldPath": "relationId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "sessions", "fields": [{"fieldPath": "contactId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "whatsapp", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "instances", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "deals", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "stage", "order": "ASCENDING"}]}, {"collectionGroup": "shotx", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "shotx", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "integrations", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "mobile", "order": "ASCENDING"}, {"fieldPath": "mobileCC", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "shotx-cron", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "scheduled_date", "order": "ASCENDING"}]}, {"collectionGroup": "accounts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "automations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "automations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "campaigns", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "campaigns", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "config.use_as_template", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "campaigns", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "categories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "title", "order": "ASCENDING"}]}, {"collectionGroup": "categories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "title", "order": "DESCENDING"}]}, {"collectionGroup": "contracts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "contracts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "config.use_as_template", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "contracts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "contracts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "use_as_template", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "cronjobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "executed", "order": "ASCENDING"}, {"fieldPath": "scheduled_date", "order": "ASCENDING"}]}, {"collectionGroup": "deals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "deals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "manager", "order": "ASCENDING"}, {"fieldPath": "funnel", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "deals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "manager", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "deals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "funnel", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "deals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "deals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "deals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "deals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "deals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "seller", "order": "ASCENDING"}, {"fieldPath": "funnel", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "deals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "seller", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "deals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tags", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "deals_keywords", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "funnel", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "deals_keywords", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "funnel", "order": "ASCENDING"}, {"fieldPath": "field", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "deals_keywords", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "deals_keywords", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "field", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "deals_keywords", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "field", "order": "ASCENDING"}, {"fieldPath": "funnel", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "deals_keywords", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "funnel", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "deals_keywords", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "funnel", "order": "ASCENDING"}, {"fieldPath": "field", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "deals_keywords", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "field", "order": "ASCENDING"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "deals_keywords", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "deals_keywords", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "field", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "emails", "queryScope": "COLLECTION", "fields": [{"fieldPath": "error", "order": "ASCENDING"}, {"fieldPath": "sent", "order": "ASCENDING"}, {"fieldPath": "sending", "order": "ASCENDING"}, {"fieldPath": "scheduled_date", "order": "ASCENDING"}]}, {"collectionGroup": "emails", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "emails", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "config.use_as_template", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "emails", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "emails", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "emails", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "emails", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "use_as_template", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "events", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "events", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "fields", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "forms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "forms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "funnels", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "funnels", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "gateway", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "title", "order": "ASCENDING"}]}, {"collectionGroup": "gateway", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "title", "order": "DESCENDING"}]}, {"collectionGroup": "integrations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "integrations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "keywords", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "collection", "order": "ASCENDING"}, {"fieldPath": "field", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "keywords", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "collection", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "keywords", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "collection", "order": "ASCENDING"}, {"fieldPath": "field", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "keywords", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "collection", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "landing-pages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "landing-pages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "leads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "email", "order": "ASCENDING"}]}, {"collectionGroup": "leads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "is_valid", "order": "ASCENDING"}, {"fieldPath": "email", "order": "ASCENDING"}]}, {"collectionGroup": "leads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "leads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "manager", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "leads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "leads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "seller", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "leads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tags", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "leads_keywords", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "field", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "leads_keywords", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "field", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "leads_keywords", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "leads_keywords", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "field", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "leads_keywords", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "mailing", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "mailing", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "config.use_as_template", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "mailing", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "chats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "users", "arrayConfig": "CONTAINS"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "from", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "to", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "from", "order": "ASCENDING"}, {"fieldPath": "createdByClient", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "to", "order": "ASCENDING"}, {"fieldPath": "createdByClient", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "config.use_as_template", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "use_as_template", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "prizes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "prizes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "qiplus-plans", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "qiplus-plans", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "qiusers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "qiusers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "affiliates", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "affiliates", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "questionnaires", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "questionnaires", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "segmentations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "segmentations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "stores", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "stores", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tags", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "title", "order": "ASCENDING"}]}, {"collectionGroup": "tags", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "title", "order": "DESCENDING"}]}, {"collectionGroup": "tags", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tasklists", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tasklists", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "teams", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "teams", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tickets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "context.collection", "order": "ASCENDING"}, {"fieldPath": "context.id", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tickets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "config.use_as_template", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tickets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tickets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "trackings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "trackings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "fields", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "trigger", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "trigger", "order": "ASCENDING"}, {"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "contactId", "order": "ASCENDING"}, {"fieldPath": "trigger", "order": "ASCENDING"}, {"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "contactId", "order": "ASCENDING"}, {"fieldPath": "trigger", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "contactId", "order": "ASCENDING"}, {"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "collection", "order": "ASCENDING"}, {"fieldPath": "fields", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "fields", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "collection", "order": "ASCENDING"}, {"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "trigger", "order": "ASCENDING"}, {"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "trigger", "order": "ASCENDING"}, {"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "keywords", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "collection", "order": "ASCENDING"}, {"fieldPath": "trigger", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "trigger", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "id", "order": "ASCENDING"}, {"fieldPath": "collection", "order": "ASCENDING"}, {"fieldPath": "trigger", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "id", "order": "ASCENDING"}, {"fieldPath": "collection", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "commissions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "commissions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "context.funnel", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "commissions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "context.funnel", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "contactId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "subscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "scheduledAction", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "currentPeriodEnd", "order": "ASCENDING"}]}, {"collectionGroup": "subscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "paymentMethod", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "currentPeriodEnd", "order": "ASCENDING"}]}, {"collectionGroup": "segmentations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "title", "order": "ASCENDING"}]}, {"collectionGroup": "subscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "scheduledAction", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "currentPeriodStart", "order": "ASCENDING"}]}, {"collectionGroup": "accounts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "owner", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "subscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "currentPeriodEnd", "order": "ASCENDING"}, {"fieldPath": "paymentMethod", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "leads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "firstName", "order": "ASCENDING"}]}, {"collectionGroup": "qiusers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "owner", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "leads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "email", "order": "ASCENDING"}, {"fieldPath": "firstName", "order": "ASCENDING"}]}, {"collectionGroup": "leads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "leads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "leads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "ID", "order": "ASCENDING"}, {"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "contactId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}], "fieldOverrides": [{"collectionGroup": "leads", "fieldPath": "tags", "indexes": [{"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "deals", "fieldPath": "tags", "indexes": [{"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "deals_keywords", "fieldPath": "createdAt", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}, {"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}, {"order": "DESCENDING", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "deals_keywords", "fieldPath": "keywords", "indexes": [{"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "deals_keywords", "fieldPath": "updatedAt", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}, {"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}, {"order": "DESCENDING", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "keywords", "fieldPath": "createdAt", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}, {"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}, {"order": "DESCENDING", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "keywords", "fieldPath": "keywords", "indexes": [{"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "keywords", "fieldPath": "updatedAt", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}, {"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}, {"order": "DESCENDING", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "leads_keywords", "fieldPath": "createdAt", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}, {"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}, {"order": "DESCENDING", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "leads_keywords", "fieldPath": "keywords", "indexes": [{"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "leads_keywords", "fieldPath": "updatedAt", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}, {"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}, {"order": "DESCENDING", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "logs", "fieldPath": "keywords", "indexes": [{"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "logs", "fieldPath": "contactId", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}, {"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "logs", "fieldPath": "data.seller", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}, {"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "logs", "fieldPath": "data.manager", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}, {"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "logs", "fieldPath": "data.team", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}, {"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}]}]}