# 📋 MAPEAMENTO DETALHADO DE CONFIGURAÇÕES

## **MAPEAMENTO: CONFIGURAÇÃO ATUAL → CONFIGURAÇÃO SEGURA**

### **🔴 CREDENCIAIS CRÍTICAS**

#### **Mailgun**
```javascript
// ❌ ATUAL (INSEGURO)
const MAILGUN_API_KEY = '**************************************************';
const MAILGUN_PUB_KEY = 'pubkey-b2e168cc42dfedf46b3aa9d752ecec1e';
const DEFAULT_MAILGUN_PASS = 'QIPlus@Amg!';

// ✅ NOVO (SEGURO)
const MAILGUN_API_KEY = functions.config().mailgun.api_key;
const MAILGUN_PUB_KEY = functions.config().mailgun.public_key;
const DEFAULT_MAILGUN_PASS = functions.config().mailgun.password;
```

#### **SMTP Admin**
```javascript
// ❌ ATUAL (INSEGURO)
const adminSMTPConfig = { 
    user: '<EMAIL>', 
    pass: 'Fck123@Amg!', 
    host: 'smtp.zoho.com',
    port: 465, 
    encryption: 'ssl' 
}

// ✅ NOVO (SEGURO)
const adminSMTPConfig = { 
    user: functions.config().smtp.admin_user || process.env.SMTP_ADMIN_USER, 
    pass: functions.config().smtp.admin_pass, 
    host: process.env.SMTP_HOST || 'smtp.zoho.com',
    port: parseInt(process.env.SMTP_PORT || '465'), 
    encryption: process.env.SMTP_ENCRYPTION || 'ssl' 
}
```

#### **PagarMe**
```javascript
// ❌ ATUAL (INSEGURO)
const PAGARME_API_KEY = APP_ENVIROINMENT === 'development' 
  ? "ak_test_9KRGIevO33Bo02hv4yBoXgrYZWCco2" 
  : "**************************************";

// ✅ NOVO (SEGURO)
const PAGARME_API_KEY = APP_ENVIROINMENT === 'development' 
  ? functions.config().pagarme.api_key_test 
  : functions.config().pagarme.api_key_live;
```

### **🟡 CONFIGURAÇÕES DE AMBIENTE**

#### **Domínios de Email**
```javascript
// ✅ MANTER (JÁ SEGURO) - Não são credenciais sensíveis
const EMAIL_APP_DOMAIN = process.env.EMAIL_APP_DOMAIN || 'qiplus.cloud';
const MAILING_DOMAIN = process.env.MAILING_DOMAIN || 'mail.qiplus.com.br';
const DEV_MAILING_DOMAIN = process.env.DEV_MAILING_DOMAIN || 'devmail.qiplus.com.br';
```

#### **URLs e Endpoints**
```javascript
// ✅ MANTER (JÁ SEGURO) - URLs públicas
const MESSAGES_ENDPOINT = `${CONSTANTS.REMOTE_URL}/mail/messages.php`;
const STORAGE_ENDPOINT = `${CONSTANTS.REMOTE_URL}/mail/store.php`;
```

### **🟢 PARÂMETROS DE PERFORMANCE**

#### **Limites e Timeouts**
```javascript
// ✅ MELHORAR - Tornar configurável
const MAILGUN_SCHEDULE_LIMIT_DAYS = parseInt(process.env.MAILGUN_SCHEDULE_LIMIT_DAYS || '3');
const EMAIL_TIMEOUT_SECONDS = parseInt(process.env.EMAIL_TIMEOUT_SECONDS || '30');
const EMAIL_BATCH_SIZE = parseInt(process.env.EMAIL_BATCH_SIZE || '50');
const EMAIL_RETRY_ATTEMPTS = parseInt(process.env.EMAIL_RETRY_ATTEMPTS || '3');
```

## **ESTRUTURA DE ARQUIVOS PROPOSTA**

### **Configuração Centralizada**
```
functions/
├── config/
│   ├── emailConfig.js          # Configurações centralizadas de email
│   ├── redisConfig.js          # Configurações Redis (já existe)
│   └── securityConfig.js       # Validações de segurança
├── emailQueue/                 # Nova arquitetura Redis para emails
│   ├── index.js               # Função principal (similar ao shotxSendMessages)
│   ├── emailProcessor.js      # Processamento de emails
│   ├── providerManager.js     # Gerenciamento de provedores
│   └── retryHandler.js        # Lógica de retry
└── .env.example               # Template de configurações
```

## **VALIDAÇÃO DE CONFIGURAÇÕES**

### **Função de Validação**
```javascript
// functions/config/securityConfig.js
const validateEmailConfig = () => {
  const requiredConfigs = [
    'mailgun.api_key',
    'mailgun.public_key', 
    'resend.api_key',
    'smtp.admin_pass'
  ];
  
  const missingConfigs = requiredConfigs.filter(config => {
    const value = functions.config()[config.split('.')[0]]?.[config.split('.')[1]];
    return !value || value === 'undefined';
  });
  
  if (missingConfigs.length > 0) {
    throw new Error(`Missing critical email configurations: ${missingConfigs.join(', ')}`);
  }
  
  console.log('✅ All email configurations validated successfully');
};
```

## **IMPACTO DA MIGRAÇÃO**

### **Arquivos que Precisam ser Modificados**
1. **functions/mailgun/index.js** - Migrar todas as credenciais hardcoded
2. **functions/init.js** - Migrar adminSMTPConfig
3. **functions/constants/index.js** - Migrar todas as API keys
4. **functions/pagarme/index.js** - Migrar credenciais PagarMe
5. **functions/mailing/index.js** - Atualizar imports e configurações
6. **functions/resend/index.js** - ✅ Já seguro, manter como está

### **Novos Arquivos a Criar**
1. **functions/config/emailConfig.js** - Configurações centralizadas
2. **functions/emailQueue/index.js** - Sistema de filas Redis
3. **functions/emailQueue/emailProcessor.js** - Processamento de emails
4. **functions/emailQueue/providerManager.js** - Gerenciamento de provedores
5. **functions/emailQueue/retryHandler.js** - Sistema de retry
6. **functions/.env.example** - Template de configurações

### **Dependências Externas**
- **Firebase Functions Config**: Para credenciais sensíveis
- **Redis**: Para sistema de filas (já configurado)
- **dotenv**: Para variáveis de ambiente (já instalado)

## **CRONOGRAMA DETALHADO**

### **Etapa 1 - Preparação (2h)**
- [ ] Criar estrutura de pastas
- [ ] Implementar função de validação de configurações
- [ ] Preparar arquivo .env.example
- [ ] Documentar todas as mudanças necessárias

### **Etapa 2 - Migração de Credenciais (4h)**
- [ ] Configurar Firebase Functions config
- [ ] Migrar arquivo por arquivo
- [ ] Testar cada migração individualmente
- [ ] Validar conectividade com provedores

### **Etapa 3 - Implementação Redis (6h)**
- [ ] Criar sistema de filas para emails
- [ ] Implementar processamento assíncrono
- [ ] Adicionar retry logic e dead letter queue
- [ ] Integrar com provedores existentes

### **Etapa 4 - Testes e Validação (4h)**
- [ ] Testes unitários para cada componente
- [ ] Testes de integração end-to-end
- [ ] Testes de performance e carga
- [ ] Validação de segurança completa
