# 🚨 PLANO DE MIGRAÇÃO DE SEGURANÇA - SISTEMA DE EMAILS

## **OBJETIVO**
<PERSON><PERSON><PERSON> to<PERSON> as credenciais hardcoded para configuração segura seguindo padrões do shotxCron.

## **CLASSIFICAÇÃO DE CRITICIDADE**

### **🔴 CRÍTICO - Ação Imediata (0-24h)**
Credenciais que representam risco financeiro ou de segurança máximo:

| ID | Credencial | Ação Requerida | Método Seguro |
|----|------------|----------------|---------------|
| C006 | PagarMe API Live | **REVOGAR IMEDIATAMENTE** | `functions.config().pagarme.api_key_live` |
| C008 | PagarMe Encrypt Live | **REVOGAR IMEDIATAMENTE** | `functions.config().pagarme.encrypt_key_live` |
| C012 | Notazz API Prod | **REVOGAR IMEDIATAMENTE** | `functions.config().notazz.api_key_prod` |
| C001 | Mailgun API Key | **REVOGAR EM 48H** | `functions.config().mailgun.api_key` |
| C004 | SMTP Admin | **ALTERAR SENHA** | `functions.config().smtp.admin_pass` |

### **🟡 ALTO - Ação em 48-72h**
Credenciais de desenvolvimento e APIs secundárias:

| ID | Credencial | Ação Requerida | Método Seguro |
|----|------------|----------------|---------------|
| C005 | PagarMe API Test | Revogar e recriar | `functions.config().pagarme.api_key_test` |
| C007 | PagarMe Encrypt Test | Revogar e recriar | `functions.config().pagarme.encrypt_key_test` |
| C013 | Notazz API Dev | Revogar e recriar | `functions.config().notazz.api_key_dev` |
| C002 | Mailgun Public Key | Revogar e recriar | `functions.config().mailgun.public_key` |
| C009 | Chat Key | Revogar e recriar | `functions.config().chat.api_key` |

### **🟢 MÉDIO - Ação em 1 semana**
Tokens e IDs menos críticos:

| ID | Credencial | Ação Requerida | Método Seguro |
|----|------------|----------------|---------------|
| C010 | Bitly Token | Revogar e recriar | `functions.config().bitly.token` |
| C011 | Google Client ID | Verificar escopo | `functions.config().google.client_id` |
| C003 | Mailgun Password | Alterar senha | `functions.config().mailgun.password` |

## **ESTRUTURA DE CONFIGURAÇÃO PROPOSTA**

### **Firebase Functions Config**
```bash
# Configurações críticas (credenciais)
firebase functions:config:set \
  mailgun.api_key="NOVA_CHAVE_MAILGUN" \
  mailgun.public_key="NOVA_CHAVE_PUBLICA" \
  mailgun.password="NOVA_SENHA_SEGURA" \
  resend.api_key="CHAVE_RESEND" \
  pagarme.api_key_live="NOVA_CHAVE_PAGARME_LIVE" \
  pagarme.api_key_test="NOVA_CHAVE_PAGARME_TEST" \
  pagarme.encrypt_key_live="NOVA_CHAVE_ENCRYPT_LIVE" \
  pagarme.encrypt_key_test="NOVA_CHAVE_ENCRYPT_TEST" \
  smtp.admin_user="<EMAIL>" \
  smtp.admin_pass="NOVA_SENHA_ADMIN" \
  chat.api_key="NOVA_CHAVE_CHAT" \
  bitly.token="NOVO_TOKEN_BITLY" \
  notazz.api_key_prod="NOVA_CHAVE_NOTAZZ_PROD" \
  notazz.api_key_dev="NOVA_CHAVE_NOTAZZ_DEV" \
  google.client_id="NOVO_CLIENT_ID_GOOGLE"
```

### **Variáveis de Ambiente (.env)**
```bash
# Configurações não-sensíveis
EMAIL_APP_DOMAIN=qiplus.cloud
MAILING_DOMAIN=mail.qiplus.com.br
DEV_MAILING_DOMAIN=devmail.qiplus.com.br
SMTP_HOST=smtp.zoho.com
SMTP_PORT=465
SMTP_ENCRYPTION=ssl
MAILGUN_SCHEDULE_LIMIT_DAYS=3
EMAIL_BATCH_SIZE=50
EMAIL_TIMEOUT_SECONDS=30
EMAIL_RETRY_ATTEMPTS=3
EMAIL_RETRY_DELAY_MS=1000
```

## **CRONOGRAMA DE EXECUÇÃO**

### **DIA 1 (HOJE) - EMERGÊNCIA**
- [ ] Revogar credenciais CRÍTICAS (C006, C008, C012)
- [ ] Gerar novas credenciais para produção
- [ ] Configurar Firebase Functions config para credenciais críticas
- [ ] Testar em ambiente de desenvolvimento

### **DIA 2-3 - MIGRAÇÃO PRINCIPAL**
- [ ] Revogar credenciais ALTO risco (C001, C004, C005, C007, C013, C002, C009)
- [ ] Implementar nova estrutura de configuração
- [ ] Migrar sistema de emails para arquitetura Redis
- [ ] Testes completos de integração

### **SEMANA 1 - FINALIZAÇÃO**
- [ ] Revogar credenciais MÉDIO risco (C010, C011, C003)
- [ ] Documentação completa
- [ ] Auditoria de segurança
- [ ] Monitoramento e alertas

## **VALIDAÇÃO DE MIGRAÇÃO**

### **Checklist de Segurança**
- [ ] Nenhuma credencial hardcoded no código
- [ ] Todas as credenciais via Firebase Functions config
- [ ] Arquivo .env.example atualizado (sem valores reais)
- [ ] .env adicionado ao .gitignore
- [ ] Histórico do Git auditado para credenciais
- [ ] Testes de conectividade com novas credenciais
- [ ] Logs de auditoria implementados

### **Testes de Funcionalidade**
- [ ] Envio de email via Mailgun funcional
- [ ] Envio de email via Resend funcional
- [ ] Envio de email via SMTP funcional
- [ ] Sistema Redis de filas operacional
- [ ] Retry logic funcionando
- [ ] Dead letter queue operacional
- [ ] Monitoramento e alertas ativos

## **PLANO DE ROLLBACK**

### **Se Falha na Migração**
1. Reverter para credenciais antigas (temporariamente)
2. Identificar e corrigir problema específico
3. Re-executar migração por etapas
4. Validar cada componente individualmente

### **Backup de Emergência**
- Manter credenciais antigas válidas por 48h após migração
- Documentar todas as alterações realizadas
- Ter script de rollback automatizado pronto
