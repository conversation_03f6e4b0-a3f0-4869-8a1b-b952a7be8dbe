# 📧 SISTEMA DE EMAILS REDIS - MIGRAÇÃO COMPLETA

## **VISÃO GERAL**

Este documento descreve a migração completa do sistema de emails do QiPlus para uma arquitetura moderna e segura baseada em Redis, seguindo os padrões do shotxCron.

### **Objetivos da Migração**
- ✅ **Eliminar credenciais hardcoded** (12+ credenciais expostas)
- ✅ **Implementar arquitetura Redis** com filas e retry logic
- ✅ **Manter compatibilidade total** com sistema atual
- ✅ **Melhorar segurança** com configurações centralizadas
- ✅ **Adicionar monitoramento** e métricas de performance

### **Status da Migração**
- 🟢 **FASE 1 COMPLETA**: Auditoria e mapeamento de configurações
- 🟢 **FASE 2 COMPLETA**: Estratégia de migração segura
- 🟢 **FASE 3 COMPLETA**: Implementação da arquitetura Redis

## **ARQUITETURA IMPLEMENTADA**

### **Estrutura de Arquivos**
```
functions/
├── config/
│   ├── emailConfig.js          # ✅ Configurações centralizadas e seguras
│   └── securityConfig.js       # ✅ Validações de segurança
├── emailQueue/                 # ✅ Sistema de filas Redis
│   ├── index.js               # ✅ Função principal (equivalente ao shotxCron)
│   ├── emailProcessor.js      # ✅ Processamento individual de emails
│   ├── providerManager.js     # ✅ Gerenciamento de provedores
│   └── retryHandler.js        # ✅ Retry logic e dead letter queue
├── .env.example               # ✅ Template de configurações
└── docs/email-migration/      # ✅ Documentação completa
```

### **Fluxo de Processamento**
```mermaid
graph TD
    A[Email Agendado] --> B[Redis Queue]
    B --> C[emailQueueProcessor]
    C --> D[emailProcessor]
    D --> E[providerManager]
    E --> F{Sucesso?}
    F -->|Sim| G[Email Enviado]
    F -->|Não| H[retryHandler]
    H --> I{Retry?}
    I -->|Sim| J[Reagendar com Backoff]
    I -->|Não| K[Dead Letter Queue]
    J --> B
```

## **CONFIGURAÇÃO E INSTALAÇÃO**

### **1. Configurar Credenciais Seguras**
```bash
# Configurar todas as credenciais via Firebase Functions config
firebase functions:config:set \
  mailgun.api_key="NOVA_CHAVE_MAILGUN" \
  mailgun.public_key="NOVA_CHAVE_PUBLICA" \
  mailgun.password="NOVA_SENHA_MAILGUN" \
  resend.apikey="CHAVE_RESEND" \
  smtp.admin_pass="NOVA_SENHA_SMTP" \
  redis.password="SENHA_REDIS"
```

### **2. Configurar Variáveis de Ambiente**
```bash
# Copiar template de configurações
cp functions/.env.example functions/.env

# Editar configurações não-sensíveis
nano functions/.env
```

### **3. Validar Configurações**
```javascript
// Executar validação de segurança
const { performSecurityAudit } = require('./config/securityConfig');
const { validateEmailConfig } = require('./config/emailConfig');

performSecurityAudit();
validateEmailConfig();
```

## **USO DO SISTEMA**

### **Processamento de Emails (Função Principal)**
```javascript
const { emailQueueProcessor } = require('./emailQueue');

// Processar emails da fila Redis
const result = await emailQueueProcessor({
  batchSize: 50,
  dryRun: false,
  validateConfig: true,
});

console.log(`Processados ${result.length} emails`);
```

### **Adicionar Email à Fila**
```javascript
const { addEmailToQueue } = require('./emailQueue');

// Adicionar email para envio imediato
await addEmailToQueue({
  to: '<EMAIL>',
  subject: 'Assunto do Email',
  html: '<h1>Conteúdo HTML</h1>',
  from: '<EMAIL>',
});

// Adicionar email agendado
await addEmailToQueue(emailData, new Date('2024-12-25T10:00:00Z'));
```

### **Monitoramento e Métricas**
```javascript
const { getEmailQueueMetrics } = require('./emailQueue');
const { getRetryMetrics } = require('./emailQueue/retryHandler');

// Métricas da fila
const queueMetrics = await getEmailQueueMetrics();
console.log('Emails na fila:', queueMetrics.scheduled_emails);

// Métricas de retry
const retryMetrics = await getRetryMetrics();
console.log('Dead letter queue:', retryMetrics.dead_letter_queue_size);
```

## **PROVEDORES SUPORTADOS**

### **1. Mailgun (Primário)**
- ✅ Configuração segura via Firebase config
- ✅ Suporte completo a recursos (tracking, tags, headers)
- ✅ Fallback automático em caso de falha

### **2. Resend (Secundário)**
- ✅ Configuração segura via Firebase config
- ✅ Interface moderna e confiável
- ✅ Compatibilidade total com dados do Mailgun

### **3. SMTP (Fallback)**
- ✅ Configuração via Zoho SMTP
- ✅ Suporte a qualquer provedor SMTP
- ✅ Último recurso em caso de falha dos outros

## **SISTEMA DE RETRY**

### **Configurações de Retry**
- **Tentativas máximas**: 3 (configurável)
- **Delay inicial**: 1000ms (configurável)
- **Backoff exponencial**: 2x (configurável)
- **Delay máximo**: 30000ms (configurável)
- **Jitter**: ±25% para evitar thundering herd

### **Análise de Erros**
```javascript
// Erros não-recuperáveis (vão direto para dead letter queue)
- 'invalid email'
- 'authentication failed'
- 'domain not found'

// Erros recuperáveis (tentativa de retry)
- 'timeout'
- 'rate limit'
- 'service unavailable'
```

## **SEGURANÇA**

### **Credenciais Eliminadas**
- ❌ **12+ credenciais hardcoded removidas**
- ✅ **Todas via Firebase Functions config**
- ✅ **Validação automática na inicialização**
- ✅ **Auditoria contínua de segurança**

### **Validações Implementadas**
```javascript
// Scan automático de credenciais expostas
const findings = scanForHardcodedCredentials();

// Validação de políticas de segurança
const policies = validateSecurityPolicies();

// Auditoria completa
const audit = performSecurityAudit();
```

## **COMPATIBILIDADE**

### **Interface Compatível**
```javascript
// Função original (deprecated)
const { emailCron } = require('./mailing');

// Nova função (recomendada)
const { emailQueueProcessor } = require('./emailQueue');

// Ambas funcionam da mesma forma
await emailCron(); // Funciona
await emailQueueProcessor(); // Recomendado
```

### **Migração Gradual**
1. **Fase 1**: Sistema atual + novo sistema em paralelo
2. **Fase 2**: Migração gradual de funcionalidades
3. **Fase 3**: Desativação do sistema antigo

## **MONITORAMENTO**

### **Métricas Disponíveis**
- **Emails na fila**: Quantidade de emails aguardando processamento
- **Taxa de sucesso**: Percentual de emails enviados com sucesso
- **Dead letter queue**: Emails que falharam definitivamente
- **Latência média**: Tempo médio de processamento
- **Throughput**: Emails processados por minuto

### **Alertas Configurados**
- **Fila muito cheia**: > 1000 emails pendentes
- **Taxa de falha alta**: > 5% de emails falhando
- **Dead letter queue crescendo**: > 10 emails/hora
- **Latência alta**: > 30s para processar email

## **TROUBLESHOOTING**

### **Problemas Comuns**

#### **1. Credenciais não configuradas**
```bash
# Erro: "Mailgun API Key não configurada"
firebase functions:config:set mailgun.api_key="SUA_CHAVE"
firebase deploy --only functions
```

#### **2. Redis não conecta**
```bash
# Verificar configuração Redis
firebase functions:config:get redis
# Configurar se necessário
firebase functions:config:set redis.host="SEU_HOST" redis.password="SUA_SENHA"
```

#### **3. Emails não sendo processados**
```javascript
// Verificar métricas
const metrics = await getEmailQueueMetrics();
console.log(metrics);

// Verificar logs
firebase functions:log --only emailQueueProcessor
```

### **Comandos Úteis**
```bash
# Ver configurações atuais
firebase functions:config:get

# Logs em tempo real
firebase functions:log --follow

# Deploy apenas das funções de email
firebase deploy --only functions:emailQueueProcessor

# Testar configurações
node -e "require('./config/securityConfig').performSecurityAudit()"
```

## **PRÓXIMOS PASSOS**

### **Implementação Imediata**
1. **Configurar credenciais** via Firebase Functions config
2. **Testar sistema** em ambiente de desenvolvimento
3. **Migrar gradualmente** emails críticos
4. **Monitorar performance** e ajustar configurações

### **Melhorias Futuras**
- **Dashboard de monitoramento** com Grafana
- **Alertas via Slack/Discord** para falhas críticas
- **Análise de performance** com métricas avançadas
- **Backup automático** de emails importantes
